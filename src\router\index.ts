import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      meta: { title: '大坝安全（首页）' }
    },
    {
      path: '/comprehensive-evaluation',
      name: 'comprehensive-evaluation',
      component: () => import('../views/ComprehensiveEvaluationView.vue'),
      meta: { title: '综合评价' }
    },
    {
      path: '/simulation-analysis',
      name: 'simulation-analysis',
      component: () => import('../views/SimulationAnalysisView.vue'),
      meta: { title: '预演分析' }
    },
    {
      path: '/warning-indicators',
      name: 'warning-indicators',
      component: () => import('../views/WarningIndicatorsView.vue'),
      meta: { title: '预警指标' }
    },
    {
      path: '/monitoring-model',
      name: 'monitoring-model',
      component: () => import('../views/MonitoringModelView.vue'),
      meta: { title: '监控模型' }
    }
  ]
})

// 设置页面标题
router.beforeEach((to, from, next) => {
  document.title = to.meta.title ? `${to.meta.title} - 大坝安全监测系统` : '大坝安全监测系统'
  next()
})

export default router
