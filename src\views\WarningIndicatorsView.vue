<script setup lang="ts">
import AppLayout from '@/components/AppLayout.vue'
import { ref, computed, onMounted, onUnmounted } from 'vue'

interface IndicatorCategory {
  id: number;
  name: string;
  count: number;
  activeWarnings: number;
}

interface Indicator {
  id: number;
  name: string;
  category: string;
  value: string;
  threshold: string;
  status: '正常' | '警告';
  trend: '上升' | '下降' | '稳定';
}

// 屏幕宽度
const screenWidth = ref(window.innerWidth)

const handleResize = () => {
  screenWidth.value = window.innerWidth
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

const isMobile = computed(() => screenWidth.value < 768)

const settingDialogWidth = computed(() => isMobile.value ? '90%' : '50%')
const warningDialogWidth = computed(() => isMobile.value ? '90%' : '70%')

// 预警指标分类
const indicatorCategories = ref<IndicatorCategory[]>([
  { id: 1, name: '水位指标', count: 5, activeWarnings: 1 },
  { id: 2, name: '结构指标', count: 8, activeWarnings: 0 },
  { id: 3, name: '渗流指标', count: 6, activeWarnings: 2 },
  { id: 4, name: '变形指标', count: 7, activeWarnings: 0 }
])

// 当前选中的分类
const currentCategory = ref<IndicatorCategory>(indicatorCategories.value[0])

// 预警指标数据
const indicators = ref<Indicator[]>([
  { id: 1, name: '库水位', category: '水位指标', value: '145.6m', threshold: '150.0m', status: '正常', trend: '上升' },
  { id: 2, name: '下游水位', category: '水位指标', value: '98.2m', threshold: '100.0m', status: '正常', trend: '稳定' },
  { id: 3, name: '水位上升速率', category: '水位指标', value: '0.8m/h', threshold: '1.0m/h', status: '警告', trend: '上升' },
  { id: 4, name: '坝顶位移', category: '结构指标', value: '2.1mm', threshold: '5.0mm', status: '正常', trend: '稳定' },
  { id: 5, name: '坝体裂缝', category: '结构指标', value: '0.2mm', threshold: '1.0mm', status: '正常', trend: '稳定' },
  { id: 6, name: '渗流量', category: '渗流指标', value: '156L/s', threshold: '150L/s', status: '警告', trend: '上升' },
  { id: 7, name: '浑浊度', category: '渗流指标', value: '48NTU', threshold: '50NTU', status: '警告', trend: '上升' }
])

// 状态筛选
const filterStatus = ref('all')

// 筛选当前分类的指标
const filteredIndicators = computed(() => {
  return indicators.value.filter(item => item.category === currentCategory.value.name)
})

// 预警历史记录
const warningHistory = ref([
  { id: 1, indicator: '渗流量', value: '158L/s', threshold: '150L/s', time: '2023-10-14 08:30:22', status: '已处理' },
  { id: 2, indicator: '浑浊度', value: '52NTU', threshold: '50NTU', time: '2023-10-13 15:45:18', status: '已处理' },
  { id: 3, indicator: '水位上升速率', value: '1.2m/h', threshold: '1.0m/h', time: '2023-10-10 09:12:05', status: '已处理' },
  { id: 4, indicator: '坝体裂缝', value: '1.1mm', threshold: '1.0mm', time: '2023-09-28 14:23:47', status: '已处理' },
  { id: 5, indicator: '渗流量', value: '162L/s', threshold: '150L/s', time: '2023-09-15 10:05:33', status: '已处理' }
])

// 对话框控制
const showSettingDialog = ref(false)
const showWarningDialog = ref(false)
const currentIndicator = ref<Indicator | null>(null)
const warningLevel = ref('3')
const notificationMethods = ref(['系统通知'])

// 打开设置对话框
const openSettingDialog = (indicator: Indicator) => {
  currentIndicator.value = indicator
  showSettingDialog.value = true
}

// 打开预警详情对话框
const openWarningDialog = (indicator: Indicator) => {
  currentIndicator.value = indicator
  showWarningDialog.value = true
}

// 关闭对话框
const handleClose = () => {
  showSettingDialog.value = false
  showWarningDialog.value = false
}

// 选择分类
const selectCategory = (category: IndicatorCategory) => {
  currentCategory.value = category
}

// 保存阈值设置
const saveThreshold = () => {
  handleClose()
  // 实际应用中这里应该有保存逻辑
}
</script>

<template>
  <AppLayout>
    <div class="indicators-container fade-in">
      <div class="page-header slide-in-left">
        <h1>预警指标</h1>
        <div class="header-actions">
          <el-button type="success" class="btn-modern">添加指标</el-button>
          <el-button type="primary" class="btn-modern">预警配置</el-button>
        </div>
      </div>
      
      <div class="categories-section slide-in-left delay-200">
        <el-card class="card-hover">
          <template #header>
            <div class="card-header">
              <span>指标分类</span>
            </div>
          </template>
          <div class="categories-list">
            <div
              v-for="category in indicatorCategories"
              :key="category.id"
              class="category-item card-hover"
              :class="{ active: currentCategory.id === category.id }"
              @click="selectCategory(category)"
            >
              <div class="category-name">{{ category.name }}</div>
              <div class="category-info">
                <span>指标数: {{ category.count }}</span>
                <el-badge :value="category.activeWarnings" :hidden="category.activeWarnings === 0" class="warning-badge" />
              </div>
            </div>
          </div>
        </el-card>
      </div>
      
      <div class="indicators-section">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>{{ currentCategory.name }} ({{ filteredIndicators.length }})</span>
              <div class="header-filters">
                <el-radio-group v-model="filterStatus" size="small">
                  <el-radio-button label="all">全部</el-radio-button>
                  <el-radio-button label="warning">预警中</el-radio-button>
                  <el-radio-button label="normal">正常</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <div class="indicators-table">
            <el-table :data="filteredIndicators" style="width: 100%">
              <el-table-column prop="name" label="指标名称" width="150" />
              <el-table-column prop="value" label="当前值" width="120" />
              <el-table-column prop="threshold" label="阈值" width="120" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.status === '正常' ? 'success' : 'warning'">
                    {{ scope.row.status }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="trend" label="趋势" width="100">
                <template #default="scope">
                  <div class="trend-indicator">
                    {{ scope.row.trend }}
                    <el-icon v-if="scope.row.trend === '上升'"><CaretTop /></el-icon>
                    <el-icon v-else-if="scope.row.trend === '下降'"><CaretBottom /></el-icon>
                    <el-icon v-else><Minus /></el-icon>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="scope">
                  <el-button type="text" @click="openSettingDialog(scope.row)">设置阈值</el-button>
                  <el-button type="text" @click="openWarningDialog(scope.row)" 
                    :disabled="scope.row.status === '正常'">查看预警</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </div>
      
      <div class="warning-history-section">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>预警历史记录</span>
            </div>
          </template>
          <div class="history-table">
            <el-table :data="warningHistory" style="width: 100%">
              <el-table-column prop="indicator" label="指标名称" width="150" />
              <el-table-column prop="value" label="触发值" width="120" />
              <el-table-column prop="threshold" label="阈值" width="120" />
              <el-table-column prop="time" label="触发时间" width="180" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag type="info">{{ scope.row.status }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default>
                  <el-button type="text">查看详情</el-button>
                </template>
              </el-table-column>
            </el-table>
            
            <div class="pagination">
              <el-pagination
                background
                layout="prev, pager, next"
                :total="50"
              />
            </div>
          </div>
        </el-card>
      </div>
    </div>
    
    <!-- 阈值设置对话框 -->
    <el-dialog
      v-model="showSettingDialog"
      title="预警阈值设置"
      :width="settingDialogWidth"
    >
      <div class="setting-form" v-if="currentIndicator">
        <el-form label-width="120px">
          <el-form-item label="指标名称">
            <el-input v-model="currentIndicator.name" disabled />
          </el-form-item>
          <el-form-item label="当前值">
            <el-input v-model="currentIndicator.value" disabled />
          </el-form-item>
          <el-form-item label="预警阈值">
            <el-input v-model="currentIndicator.threshold" />
          </el-form-item>
          <el-form-item label="预警级别">
            <el-select v-model="warningLevel" placeholder="请选择预警级别">
              <el-option label="一级 (紧急)" value="1" />
              <el-option label="二级 (严重)" value="2" />
              <el-option label="三级 (警告)" value="3" />
              <el-option label="四级 (注意)" value="4" />
            </el-select>
          </el-form-item>
          <el-form-item label="通知方式">
            <el-checkbox-group v-model="notificationMethods">
              <el-checkbox label="系统通知" />
              <el-checkbox label="短信" />
              <el-checkbox label="邮件" />
              <el-checkbox label="电话" />
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="saveThreshold">保存</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 预警详情对话框 -->
    <el-dialog
      v-model="showWarningDialog"
      title="预警详情"
      :width="warningDialogWidth"
    >
      <div class="warning-details" v-if="currentIndicator">
        <div class="warning-header">
          <h2>{{ currentIndicator.name }} - 预警信息</h2>
          <el-tag :type="currentIndicator.status === '正常' ? 'success' : 'warning'" size="large">
            {{ currentIndicator.status }}
          </el-tag>
        </div>
        
        <div class="warning-info">
          <div class="info-item">
            <div class="info-label">当前值</div>
            <div class="info-value">{{ currentIndicator.value }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">阈值</div>
            <div class="info-value">{{ currentIndicator.threshold }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">趋势</div>
            <div class="info-value">{{ currentIndicator.trend }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">预警时间</div>
            <div class="info-value">2023-10-14 10:25:36</div>
          </div>
        </div>
        
        <div class="warning-chart">
          <div class="chart-placeholder">
            图表区域 - 指标变化趋势
          </div>
        </div>
        
        <div class="warning-actions">
          <h3>处理建议</h3>
          <ul>
            <li>检查相关监测设备是否正常工作</li>
            <li>增加监测频率，密切关注指标变化</li>
            <li>通知相关负责人进行现场检查</li>
            <li>准备应急预案，以防指标继续恶化</li>
          </ul>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">关闭</el-button>
          <el-button type="primary">标记为已处理</el-button>
        </span>
      </template>
    </el-dialog>
  </AppLayout>
</template>

<style scoped>
.indicators-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

h1 {
  margin: 0;
  color: #303133;
}

.categories-section {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.categories-list {
  display: flex;
  gap: 15px;
  padding: 10px 0;
}

.category-item {
  padding: 15px 20px;
  border-radius: 4px;
  background-color: #f5f7fa;
  cursor: pointer;
  min-width: 120px;
  transition: all 0.3s;
}

.category-item:hover {
  background-color: #e6f1fc;
}

.category-item.active {
  background-color: #409EFF;
  color: white;
}

.category-name {
  font-weight: bold;
  margin-bottom: 8px;
}

.category-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.indicators-section {
  margin-bottom: 20px;
}

.header-filters {
  display: flex;
  align-items: center;
}

.indicators-table, .history-table {
  padding: 10px 0;
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: 5px;
}

.warning-history-section {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.setting-form {
  padding: 20px 0;
}

.warning-details {
  padding: 20px 0;
}

.warning-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.warning-header h2 {
  margin: 0;
}

.warning-info {
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
}

.info-item {
  flex: 1;
  text-align: center;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.info-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.info-value {
  font-size: 22px;
  font-weight: bold;
  color: #303133;
}

.warning-chart {
  margin-bottom: 30px;
}

.chart-placeholder {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  color: #909399;
  border-radius: 4px;
}

.warning-actions {
  margin-bottom: 20px;
}

.warning-actions h3 {
  margin-bottom: 15px;
}

.warning-actions ul {
  padding-left: 20px;
}

.warning-actions li {
  margin-bottom: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  .header-actions {
    width: 100%;
  }
  .categories-list {
    flex-wrap: wrap;
  }
  .warning-info {
    flex-direction: column;
  }
}
</style> 