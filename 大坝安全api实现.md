# 大坝安全系统 API 文档

## 基础信息

**Base URL**: 待定

### 通用响应格式

#### 成功响应
```json
{
  "code": 200,
  "message": "请求成功",
  "data": { ... } // 具体数据结构见各接口定义
}
```

#### 错误响应

**资源未找到 (404 Not Found)**
```json
{
  "code": 404,
  "message": "指定的大坝不存在",
  "data": null
}
```

**参数无效 (400 Bad Request)**
```json
{
  "code": 400,
  "message": "无效的页码参数",
  "data": null
}
```

---

## API 接口列表

### 0. 获取大坝列表接口

**功能**: 获取大坝下拉列表  
**路径**: `/api/v1/dams`  
**方法**: `GET`  

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": "dam_qs",          // 大坝的唯一标识符 (damId)，返回的 id 字段将作为 damId 用于所有其他接口的路径参数
      "name": "青山水库大坝"    // 用于在下拉列表中显示的名称
    },
    {
      "id": "dam_sh",
      "name": "石河水库大坝"
    },
    {
      "id": "dam_qsh",
      "name": "清水河水库大坝"
    }
  ]
}
```

---

### 1. 综合评判接口

**功能**: 获取综合评判雷达图数据  
**路径**: `/api/v1/dams/{damId}/assessment`  
**方法**: `GET`  

**路径参数**:

| 参数名 | 类型   | 是否必须 | 描述              |
|--------|--------|----------|-------------------|
| damId  | String | 是       | 大坝的唯一标识符  |

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    //渗流
    "seepage": {
      "status": "NORMAL", // 枚举值: "NORMAL" (正常), "ABNORMAL" (异常)
      "chartData": {
        "labels": ["主坝渗漏量", "主坝渗流", "绕坝渗流", "泄洪闸扬压力"],
        "values": [65, 70, 80, 60]
      }
    },
    //变形
    "deformation": {
      "status": "NORMAL",
      "chartData": {
        "labels": ["主坝水平位移", "泄洪闸沉降", "泄洪闸水平位移", "主坝沉降"],
        "values": [50, 60, 75, 55]
      }
    }
  }
}
```

---

### 2. 大坝安全关键指标接口

**功能**: 获取大坝安全关键指标  
**路径**: `/api/v1/dams/{damId}/key-indicators`  
**方法**: `GET`  

**路径参数**:

| 参数名 | 类型   | 是否必须 | 描述              |
|--------|--------|----------|-------------------|
| damId  | String | 是       | 大坝的唯一标识符  |

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "indicatorId": "upstream_water_level_max", // 指标唯一ID
      "name": "上游最高水位",                    // 指标名称 (用于显示)
      "value": 23.2,                            // 当前值
      "unit": "m",                              // 单位
      "date": "2025-06-20"             // 数据记录时间
    },
    {
      "indicatorId": "dam_seepage_max",
      "name": "主坝最大渗流量",
      "value": 1.5,
      "unit": "L/s",
      "date": "2025-06-20"
    },
    {
      "indicatorId": "dam_displacement_cumulative",
      "name": "主坝累计水平位移",
      "value": 5.8,
      "unit": "mm",
      "date": "2025-06-20",
      "historicalMax": { // 历史最大值
        "value": 6.1,
        "date": "2024-08-15"
      }
    },
    {
      "indicatorId": "dam_settlement_cumulative",
      "name": "主坝累计沉降",
      "value": 3.4,
      "unit": "mm",
      "date": "2025-06-20",
      "historicalMax": {
        "value": 3.4,
        "date": "2025-06-20"
      }
    }
  ]
}
```

---

### 3. 历史报警列表接口

**功能**: 获取历史报警列表 (分页)  
**路径**: `/api/v1/dams/{damId}/alarms`  
**方法**: `GET`  

**路径参数**:

| 参数名 | 类型   | 是否必须 | 描述              |
|--------|--------|----------|-------------------|
| damId  | String | 是       | 大坝的唯一标识符  |

**查询参数**:

| 参数名    | 类型    | 是否必须 | 默认值 | 描述             |
|-----------|---------|----------|--------|------------------|
| pageNum   | Integer | 否       | 1      | 查询的页码       |
| pageSize  | Integer | 否       | 20     | 每页记录数量     |

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 78,           // 总记录数
    "pageNum": 1,          // 当前页码
    "pageSize": 20,        // 每页数量
    "list": [
      {
        "id": 1,                     // 报警记录唯一ID
        "date": "2025-06-20",        // 报警时间
        "monitoringPoint": "TPX-01",          // 监测点编号
        "engineeringPart": "主坝5#坝段",      // 所属工程部位
        "warningInfo": "渗流量超过阈值 1.5 L/s" // 详细报警信息
      },
      {
        "id": 2,
        "date": "2025-06-19",
        "monitoringPoint": "H-disp-03",
        "engineeringPart": "泄洪闸右岸",
        "warningInfo": "水平位移速率异常"
      }
    ]
  }
}
```

---

### 4. 当前库水位接口

**功能**: 获取指定大坝的当前库水位  
**路径**: `/api/v1/dams/{damId}/water-level`  
**方法**: `GET`  

**路径参数**:

| 参数名 | 类型   | 是否必须 | 描述              |
|--------|--------|----------|-------------------|
| damId  | String | 是       | 大坝的唯一标识符  |

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "value": 23.2,                      // 当前水位值
    "unit": "m",                        // 单位: 米
    "updateTime": "2025-06-21"          // 数据更新时间
  }
}
```

---

### 5. 历史评估结果接口

**功能**: 获取指定大坝的最新评估结果，以及可分页的历史评估记录  
**路径**: `/api/v1/dams/{damId}/assessments`  
**方法**: `GET`  

**路径参数**:

| 参数名 | 类型   | 是否必须 | 描述              |
|--------|--------|----------|-------------------|
| damId  | String | 是       | 大坝的唯一标识符  |

**查询参数**:

| 参数名    | 类型    | 是否必须 | 默认值 | 描述             |
|-----------|---------|----------|--------|------------------|
| pageNum   | Integer | 否       | 1      | 查询的页码       |
| pageSize  | Integer | 否       | 20     | 每页记录数量     |

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    // 对应 "当前评估结果"，此处认为是最新的评估结果
    "latestResult": {
      "id": 78,                               // 最新评估记录的ID
      "status": "SAFE",                       // 枚举值: "SAFE"(安全), "ABNORMAL"(异常)
      "statusText": "安全",                   // 用于UI展示的文本
      "updateTime": "2025-06-20"     // 最后更新时间
    },
    // 对应 "评估结果列表"
    "history": {
      "total": 78,
      "pageNum": 1,
      "pageSize": 20,
      "list": [
        {
          "id": 78,                     // 评估唯一ID，用于"查看"操作
          "analysisTime": "2025-06-20", // 分析时间
          "status": "SAFE",
          "statusText": "安全"
        },
        {
          "id": 77,
          "analysisTime": "2025-06-19",
          "status": "SAFE",
          "statusText": "安全"
        },
        {
          "id": 76,
          "analysisTime": "2025-06-18",
          "status": "ABNORMAL",
          "statusText": "异常"
        }
        // ... 更多记录
      ]
    }
  }
}
```
---

### 5.1 发起评估接口 

**功能**: 创建一个新的大坝综合评估任务。服务器接收到请求后，将开始在后台执行评估计算。  
**路径**: `/api/v1/dams/{damId}/assessments`  
**方法**: `POST`  


**路径参数**:

| 参数名 | 类型   | 是否必须 | 描述              |
|--------|--------|----------|-------------------|
| damId  | String | 是       | 大坝的唯一标识符  |

**请求体**:  
无
```

**成功响应 (202 Accepted)**:  
```json
{
  "code": 202,
  "message": "评估任务已创建，正在后台处理中。",
  "data": {
    "assessmentId": "assess-task-9f8e7d6c5b4a" // 新创建的评估任务的唯一ID
  }
}
```
---

### 6. 预警指标级联查询接口

**功能**: 根据指定的筛选条件，分页获取大坝各测点的预警阈值  
**路径**: `/api/v1/dams/{damId}/thresholds`  
**方法**: `GET`  

> UI中的筛选条件"具体项目"、"断面"、"测点"是联动的。为了填充这些下拉框，需要提供辅助接口，如下：  
> - `GET /api/v1/dams/{damId}/monitoring-items` (获取所有具体项目)  
> - `GET /api/v1/dams/{damId}/sections?itemId={itemId}` (根据项目ID获取断面列表)  
> - `GET /api/v1/dams/{damId}/points?sectionId={sectionId}` (根据断面ID获取测点列表)  

**路径参数**:

| 参数名 | 类型   | 是否必须 | 描述              |
|--------|--------|----------|-------------------|
| damId  | String | 是       | 大坝的唯一标识符  |

**查询参数**:

| 参数名    | 类型    | 是否必须 | 描述                                  | 示例           |
|-----------|---------|----------|---------------------------------------|----------------|
| itemId    | String  | 是       | 具体项目的唯一ID (例如，"主坝沉降"的ID) | dam_settlement |
| sectionId | String  | 否       | 断面的唯一ID (例如, "坝0+101.00"的ID)  | section_0_101  |
| pointCode | String  | 否       | 测点编号，支持模糊查询                | S1-1           |
| pageNum   | Integer | 否       | 查询的页码，默认为 1                  | 1              |
| pageSize  | Integer | 否       | 每页记录数量，默认为 20               | 20             |

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "unit": "mm",                         // 当前查询指标的单位
    "lastUpdateTime": "2025-06-15", // 预警阈值的最后更新/计算时间
    "pagination": {
      "total": 78,                        // 总记录数
      "pageNum": 1,                       // 当前页码
      "pageSize": 20                      // 每页数量
    },
    "list": [
      {
        "pointCode": "S1-1",              // 测点编号
        "levels": {                       // 各级预警值
          "level1": 2.23,
          "level2": 2.23,
          "level3": 2.23
        }
      },
      {
        "pointCode": "S1-2",
        "levels": {
          "level1": 2.23,
          "level2": 2.23,
          "level3": 2.23
        }
      },
      {
        "pointCode": "S1-3",
        "levels": {
          "level1": 2.45,
          "level2": 2.80,
          "level3": 3.10
        }
      }
      // ... 更多记录
    ]
  }
}
```
---

### 6.1 获取监测项目列表

**功能**: 获取预警指标筛选所需的"具体项目"下拉列表  
**路径**: `/api/v1/dams/{damId}/monitoring-items`  
**方法**: `GET`  

**路径参数**:

| 参数名 | 类型   | 是否必须 | 描述              |
|--------|--------|----------|-------------------|
| damId  | String | 是       | 大坝的唯一标识符  |

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": "dam_settlement",  // 用于后续接口调用的唯一ID
      "name": "主坝沉降"        // 用于UI显示的名称
    },
    {
      "id": "dam_displacement",
      "name": "主坝水平位移"
    },
    {
      "id": "seepage",
      "name": "渗流"
    }
  ]
}
```

---

### 6.2 获取断面列表

**功能**: 根据指定的监测项目，获取其下的"断面"下拉列表  
**路径**: `/api/v1/dams/{damId}/sections`  
**方法**: `GET`  

**路径参数**:

| 参数名 | 类型   | 是否必须 | 描述                                 |
|--------|--------|----------|------------------------------------- |
| damId  | String | 是       | 大坝的唯一标识符                     |
| itemId | String | 是       | 监测项目的唯一ID (来自6.1接口的id字段)|

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": "section_0_101",   // 用于后续接口调用的唯一ID
      "name": "坝0+101.00"      // 用于UI显示的名称
    },
    {
      "id": "section_0_250",
      "name": "坝0+250.00"
    },
    {
      "id": "section_spillway",
      "name": "泄洪闸断面"
    }
  ]
}
```

---

### 6.3 获取测点列表

**功能**: 根据指定的监测项目和断面，获取其下的"测点"下拉列表  
**路径**: `/api/v1/dams/{damId}/monitoring-points`  
**方法**: `GET`  

**路径参数**:

| 参数名    | 类型   | 是否必须 | 描述                                 |
|-----------|--------|----------|------------------------------------- |
| damId     | String | 是       | 大坝的唯一标识符                     |
| itemId    | String | 是       | 监测项目的唯一ID (来自6.1接口)       |
| sectionId | String | 是       | 断面的唯一ID (来自6.2接口)           |

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": "S1-1",  // 用于主查询接口的 pointCode
      "name": "S1-1"  // 用于UI显示的名称
    },
    {
      "id": "S1-2",
      "name": "S1-2"
    },
    {
      "id": "S1-3",
      "name": "S1-3"
    }
  ]
}
```

---

### 7. 预演分析接口

**说明**: 预演分析可以采用异步任务模式进行设计。客户端首先通过 POST 请求创建一个分析任务，服务器立即返回一个任务ID，并开始在后台进行计算。客户端可以通过任务ID轮询任务状态来查看最新状态。当任务完成后，再获取详细结果进行展示。

#### 7.1 创建预演分析任务

**功能**: 根据用户输入的参数，创建一个新的预演分析任务  
**路径**: `/api/v1/dams/{damId}/forecast-analyses`  
**方法**: `POST`  

**路径参数**:

| 参数名 | 类型   | 是否必须 | 描述              |
|--------|--------|----------|-------------------|
| damId  | String | 是       | 大坝的唯一标识符  |

**请求体**:
```json
{
  "upstreamWaterLevel": 23.2,
  "forecastDuration": 1
}
```

**响应数据**: 返回 202 Accepted 表示服务器已接收请求并开始处理，响应体中包含任务ID，用于后续查询
```json
{
  "code": 202,
  "message": "预演分析任务已创建，正在后台计算中。",
  "data": {
    "taskId": "f-analysis-a1b2c3d4"
  }
}
```

---

#### 7.2 获取预演分析历史列表

**功能**: 分页获取预演分析的历史记录，用于填充右侧列表  
**路径**: `/api/v1/dams/{damId}/forecast-analyses`  
**方法**: `GET`  

**路径参数**:

| 参数名 | 类型   | 是否必须 | 描述              |
|--------|--------|----------|-------------------|
| damId  | String | 是       | 大坝的唯一标识符  |

**查询参数**:

| 参数名    | 类型    | 是否必须 | 默认值 | 描述             |
|-----------|---------|----------|--------|------------------|
| pageNum   | Integer | 否       | 1      | 查询的页码       |
| pageSize  | Integer | 否       | 20     | 每页记录数量     |

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 78,
    "pageNum": 1,
    "pageSize": 20,
    "list": [
      {
        "id": "f-analysis-a1b2c3d4",         // 任务唯一ID
        "analysisTime": "2025-06-20",
        "status": "RUNNING",                // 枚举: PENDING, RUNNING, COMPLETED, FAILED
        "result": null                      // 任务未完成时，结果为空
      },
      {
        "id": "f-analysis-a1b2c3d3",
        "analysisTime": "2025-06-19",
        "status": "COMPLETED",
        "result": {
          "limitWaterLevel": 22.3,          // 极限水位
          "unit": "m"
        }
      }
    ]
  }
}
```

---

#### 7.3 获取单个预演分析结果详情

**功能**: 获取指定预演分析任务的详细信息，包括状态和用于3D可视化的模型数据  
**路径**: `/api/v1/dams/{damId}/forecast-analyses/{analysisId}`  
**方法**: `GET`  

**路径参数**:

| 参数名     | 类型   | 是否必须 | 描述              |
|------------|--------|----------|-------------------|
| damId      | String | 是       | 大坝的唯一标识符  |
| analysisId | String | 是       | 预演分析任务的ID  |

**响应数据** (任务进行中):
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": "f-analysis-a1b2c3d4",
    "analysisTime": "2025-06-20",
    "status": "RUNNING",                      // 状态
    "progress": 70,                           // 计算进度 (0-100)
    "estimatedTimeRemaining": 23,             // 预估剩余时间 (秒)
    "inputParams": {                          // 输入参数回显
      "upstreamWaterLevel": 23.2,
      "forecastDuration": 1
    },
    "result": null,                           // 任务未完成，结果为空
    "modelData": null                         // 任务未完成，模型数据为空
  }
}
```

**响应数据** (任务已完成):
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": "f-analysis-a1b2c3d3",
    "analysisTime": "2025-06-19",
    "status": "COMPLETED",
    "progress": 100,
    "estimatedTimeRemaining": 0,
    "inputParams": { /* ... 输入参数 ... */ },
    "result": {
      "limitWaterLevel": 22.3,
      "unit": "m"
    },
    "modelData": { // 3D模型数据，具体结构确定前端3D渲染引擎
      "vertices": [0,0,0, 1,0,0, ...],       // 顶点坐标
      "indices": [0,1,2, ...],              // 面索引
      "stressValues": [0.5, 0.8, ...],      // 每个顶点的应力值，用于着色
      "sectionPlanes": { /* ... 剖面信息 ... */ }              // 剖面信息
    }
  }
}
```

---

### 8. 参数反演接口

**设计说明**: 参数反演是根据实测数据反推水工建筑物的物理力学参数的过程，同样是一个耗时的计算过程，因此采用异步任务模式进行设计。客户端首先通过 POST 请求创建一个反演任务，服务器立即返回一个任务ID，并开始在后台进行计算。客户端可以通过任务ID轮询任务状态，以获取实时进度更新。当任务完成后，再获取详细结果进行展示。

#### 8.1 创建反演任务

**功能**: 根据用户输入的参数，创建一个新的参数反演任务  
**路径**: `/api/v1/dams/{damId}/parameter-inversions`  
**方法**: `POST`  

**路径参数**:

| 参数名 | 类型   | 是否必须 | 描述              |
|--------|--------|----------|-------------------|
| damId  | String | 是       | 大坝的唯一标识符  |

**请求体**:
```json
{
  "monitoringData": {
    "startDate": "2025-01-01",
    "endDate": "2025-06-20"
  },
  "targetParameters": ["permeability", "elasticModulus", "poissonRatio"],
  "iterationLimit": 100,
  "convergenceThreshold": 0.001
}
```

**响应数据**: 返回 202 Accepted 表示服务器已接收请求并开始处理，响应体中包含任务ID，用于后续查询
```json
{
  "code": 202,
  "message": "参数反演任务已创建，正在后台计算中。",
  "data": {
    "taskId": "p-inversion-e5f6g7h8"
  }
}
```

---

#### 8.2 获取反演历史

**功能**: 分页获取参数反演的历史记录，用于填充历史列表  
**路径**: `/api/v1/dams/{damId}/parameter-inversions`  
**方法**: `GET`  

**路径参数**:

| 参数名 | 类型   | 是否必须 | 描述              |
|--------|--------|----------|-------------------|
| damId  | String | 是       | 大坝的唯一标识符  |

**查询参数**:

| 参数名    | 类型    | 是否必须 | 默认值 | 描述             |
|-----------|---------|----------|--------|------------------|
| pageNum   | Integer | 否       | 1      | 查询的页码       |
| pageSize  | Integer | 否       | 20     | 每页记录数量     |

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 45,
    "pageNum": 1,
    "pageSize": 20,
    "list": [
      {
        "id": "p-inversion-e5f6g7h8",         // 任务唯一ID
        "inversionTime": "2025-06-20"
      },
      {
        "id": "p-inversion-e5f6g7h7",
        "inversionTime": "2025-06-19"
        }
      }
    ]
  }
}
```

---

#### 8.3 获取单个反演结果详情

**功能**: 获取指定参数反演任务的详细信息，包括状态、进度和计算结果  
**路径**: `/api/v1/dams/{damId}/parameter-inversions/{inversionId}`  
**方法**: `GET`  

**路径参数**:

| 参数名      | 类型   | 是否必须 | 描述              |
|-------------|--------|----------|-------------------|
| damId       | String | 是       | 大坝的唯一标识符  |
| inversionId | String | 是       | 反演任务的ID      |



#### 8.4 获取反演任务进度

**功能**: 获取指定反演任务的进度信息（轻量级接口，适合频繁轮询）  
**路径**: `/api/v1/dams/{damId}/parameter-inversions/{inversionId}/progress`  
**方法**: `GET`  

**路径参数**:

| 参数名      | 类型   | 是否必须 | 描述              |
|-------------|--------|----------|-------------------|
| damId       | String | 是       | 大坝的唯一标识符  |
| inversionId | String | 是       | 反演任务的ID      |

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "status": "RUNNING",                // 任务状态: PENDING, RUNNING, COMPLETED, FAILED
    "progress": 65,                     // 计算进度 (0-100)
    "estimatedTimeRemaining": 23,       // 预估剩余时间 (秒)
  }
}
```

---

### 9. 导入数据接口

**功能**: 上传用于预演分析的外部数据文件  
**路径**: `/api/v1/dams/{damId}/forecast-analyses/import`  
**方法**: `POST`  

**请求格式**: `multipart/form-data`

**路径参数**:

| 参数名 | 类型   | 是否必须 | 描述              |
|--------|--------|----------|-------------------|
| damId  | String | 是       | 大坝的唯一标识符  |

**表单参数**:

| 参数名 | 类型 | 描述                       |
|--------|------|-----------------------------|
| file   | File | 要上传的数据文件 (如 .csv, .txt) |

**响应数据**:
```json
{
  "code": 200,
  "message": "文件上传成功，数据已导入。",
  "data": null
}
```

---

### 10. 监控模型性能接口

**说明**: 本接口用于获取指定测点上，特定监控模型的性能数据，包括训练集/测试集的拟合曲线和性能指标。本接口依赖于 6.1, 6.2, 6.3 等辅助接口来获取筛选条件（项目、断面、测点）的下拉选项。  
**功能**: 获取监控模型的性能详情。  
**路径**: `/api/v1/dams/{damId}/monitoring-models/performance`  
**方法**: `GET`  

**路径参数**:

| 参数名 | 类型   | 是否必须 | 描述              |
|--------|--------|----------|-------------------|
| damId  | String | 是       | 大坝的唯一标识符  |

**查询参数**:

| 参数名    | 类型   | 是否必须 | 描述                             |
|-----------|--------|----------|----------------------------------|
| itemId    | String | 是       | 具体项目的唯一ID (来自接口6.1)   |
| sectionId | String | 是       | 断面的唯一ID (来自接口6.2)       |
| pointCode | String | 是       | 测点编号 (来自接口6.3)           |
| modelType | String | 是       | 模型类型。枚举值: STATISTICAL (统计模型), PINN (物理信息神经网络), BPNN (BP神经网络) |

**成功响应 (200 OK)**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "lastUpdateTime": "2025-06-15", // 模型最后训练/更新时间

    // 训练集数据
    "trainingData": {
      "chartData": {
        "title": "训练集拟合曲线",
        "labels": [0, 10, 20, 30, 40, 50, "..."], // X轴标签 (例如时间或样本点)
        "actualValues": [22.5, 23.1, 24.5, 26.0, 25.5, 24.8, "..."], // 真实值
        "predictedValues": [22.6, 23.0, 24.7, 25.8, 25.6, 24.7, "..."] // 预测值
      },
      "metrics": {
        "mse": 0.034, // 均方误差
        "r2": 0.941   // 决定系数
      }
    },

    // 测试集数据
    "testingData": {
      "chartData": {
        "title": "测试集拟合曲线",
        "labels": [0, 5, 10, 15, 20, "..."],
        "actualValues": [21.50, 21.75, 22.00, 21.80, 21.60, "..."],
        "predictedValues": [21.55, 21.70, 22.05, 21.75, 21.65, "..."]
      },
      "metrics": {
        "mse": 0.020,
        "r2": 0.828
      }
    }
  }
}
```

---

### 11. 监控模型弹窗接口

#### 11.1 性能指标对比

**功能**: 获取指定测点上，所有可用监控模型在性能指标（MAE, R²）上的具体数值，用于横向对比。  
**路径**: `/api/v1/dams/{damId}/monitoring-models/performance-comparison`  
**方法**: `GET`  

> 本接口依赖于 6.1, 6.2, 6.3 等辅助接口来获取筛选条件（项目、断面、测点）的下拉选项。

**路径参数**:

| 参数名 | 类型   | 是否必须 | 描述              |
|--------|--------|----------|-------------------|
| damId  | String | 是       | 大坝的唯一标识符  |

**查询参数**:

| 参数名    | 类型   | 是否必须 | 描述                             |
|-----------|--------|----------|----------------------------------|
| itemId    | String | 是       | 具体项目的唯一ID                 |
| sectionId | String | 是       | 断面的唯一ID                     |
| pointCode | String | 是       | 测点编号                         |

**说明**: 返回数据结构以模型为单位，每个模型包含一个metrics对象，方便前端根据用户选择的指标（如mae）动态提取数值进行绘图。

**成功响应 (200 OK)**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "lastUpdateTime": "2025-06-15", // 指标的最后计算时间
    "comparison": [
      {
        "modelId": "pinn",
        "modelName": "PINNs",
        "metrics": {
          "mae": 0.094,
          "r2": 0.941
        }
      },
      {
        "modelId": "svm",
        "modelName": "SVM",
        "metrics": {
          "mae": 0.204,
          "r2": 0.882
        }
      },
      {
        "modelId": "lstm",
        "modelName": "LSTM",
        "metrics": {
          "mae": 0.282,
          "r2": 0.850
        }
      },
      {
        "modelId": "bp",
        "modelName": "BP",
        "metrics": {
          "mae": 0.708,
          "r2": 0.766
        }
      }
    ]
  }
}
```

---

#### 11.2 性态预测

**功能**: 接收用户选择的测点、模型和预测日期，进行计算并返回预测结果以及用于展示的拟合曲线。  
**路径**: `/api/v1/dams/{damId}/monitoring-models/predict`  
**方法**: `POST`  

> 本接口依赖于 6.1, 6.2, 6.3 等辅助接口来获取筛选条件（项目、断面、测点）的下拉选项。

**说明**: 这是一个动作接口，客户端发送计算请求，服务器返回计算结果。

**路径参数**:

| 参数名 | 类型   | 是否必须 | 描述              |
|--------|--------|----------|-------------------|
| damId  | String | 是       | 大坝的唯一标识符  |

**查询参数**:

| 参数名    | 类型   | 是否必须 | 描述                             |
|-----------|--------|----------|----------------------------------|
| itemId    | String | 是       | 具体项目的唯一ID                 |
| sectionId | String | 是       | 断面的唯一ID                     |
| pointCode | String | 是       | 测点编号                         |

**说明**: 响应体包含了渲染整个弹窗所需的所有信息，包括真实值和预测值，以及预测结果评估信息。

**成功响应 (200 OK)**:
```json
{
  "code": 200,
  "message": "预测成功",
  "data": {
    // 用于渲染拟合曲线图的数据
    "fitData": {
      "title": "训练集拟合曲线",
      "labels": [0, 10, 20, "..."], // X轴标签 (样本点)
      "actualValues": [21.00, 21.25, 21.50, "..."], // 真实值
      "predictedValues": [21.05, 21.30, 21.45, "..."] // 预测值
    },
    // 用于显示在底部的信息
    "predictionResult": {
      "predictionDate": "2025-06-30",
      "evaluation": "SAFE", // 枚举值: SAFE
      "evaluationText": "安全" // 用于UI显示的文本
    }
  }
}
```

---

### 13. 预警处理相关接口

#### 13.1 获取预警详情及处理表单数据

**功能**: 在打开"预警信息"处理弹窗时调用。获取指定预警的详细信息，以及处理该预警所需的表单选项（如处置方式、可选用户列表等）。  
**路径**: `/api/v1/dams/{damId}/alarms/{alarmId}/handle-options`  
**方法**: `GET`  

**路径参数**:

| 参数名 | 类型    | 是否必须 | 描述                             |
|--------|---------|----------|----------------------------------|
| damId  | String  | 是       | 大坝的唯一标识符                 |
| alarmId| Integer | 是       | 要处理的预警记录的唯一ID (来自历史报警列表) |

**成功响应 (200 OK)**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    // 自动获取的预警信息
    "alarmInfo": {
      "type": "测值异常",
      "time": "2025-06-25",
      "content": "S2-2测值异常; S3-4测值异常"
    },
    // 用于填充表单的数据
    "formOptions": {
      // 处置方式下拉选项
      "handlingMethods": [
        { "id": "system_notify", "name": "系统通知" },
        { "id": "manual_review", "name": "人工复核" },
        { "id": "ignore", "name": "忽略" }
      ],
      // 接收人和审批人下拉选项 (通常是系统内的用户列表)
      "users": [
        { "userId": "user001", "userName": "张三", "role": "operator" },
        { "userId": "user002", "userName": "李四", "role": "operator" },
        { "userId": "user003", "userName": "王五", "role": "approver" }
      ]
    },
    // 建议的默认处理内容 (后端可根据预警内容智能生成)
    "defaultContent": {
      "suggestion": "对S2-2, S3-4加密观测",
      "notification": "S2-2测值异常; S3-4测值异常。请对S2-2, S3-4加密观测，检测是否为仪器数值波动。"
    }
  }
}
```

---

#### 13.2 处理预警信息

**功能**: 用户填写完处理表单后，点击"处理"按钮时调用。提交处理结果，将预警标记为已处理或进入审批流程。  
**路径**: `/api/v1/dams/{damId}/alarms/{alarmId}/handle`  
**方法**: `POST`  

**说明**: 这是一个动作接口，用于更新一个已存在的预警记录。

**路径参数**:

| 参数名 | 类型    | 是否必须 | 描述              |
|--------|---------|----------|-------------------|
| damId  | String  | 是       | 大坝的唯一标识符  |
| alarmId| Integer | 是       | 要处理的预警记录的唯一ID |

**请求体**:
```json
{
  "handlingMethodId": "system_notify", // 处置方式ID
  "suggestion": "对S2-2, S3-4加密观测",   // 处理建议
  "notificationContent": "S2-2测值异常; S3-4测值异常。请对S2-2, S3-4加密观测，检测是否为仪器数值波动。", // 通知内容
  "recipientIds": ["user001", "user002"], // 接收人用户ID列表
  "approverIds": ["user003"]               // 审批人用户ID列表
}
```

**成功响应 (200 OK)**:
```json
{
  "code": 200,
  "message": "预警处理成功",
  "data": null
}
```

**失败响应 (400 Bad Request)**:
```json
{
  "code": 400,
  "message": "请求参数错误：处理建议不能为空。",
  "data": null
}
```
