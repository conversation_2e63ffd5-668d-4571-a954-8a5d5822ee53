<script setup lang="ts">
import AppLayout from '@/components/AppLayout.vue'
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'

// 屏幕宽度
const screenWidth = ref(window.innerWidth)

const handleResize = () => {
  screenWidth.value = window.innerWidth
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

const isMobile = computed(() => screenWidth.value < 768)

const paramsDialogWidth = computed(() => isMobile.value ? '90%' : '50%')
const resultsDialogWidth = computed(() => isMobile.value ? '90%' : '70%')

// 模拟场景数据
const scenarios = ref([
  { id: 1, name: '洪水情景', description: '模拟极端洪水条件下大坝的安全状态', status: '已完成' },
  { id: 2, name: '地震情景', description: '模拟7级地震条件下大坝的结构稳定性', status: '进行中' },
  { id: 3, name: '连续降雨情景', description: '模拟连续30天强降雨对大坝的影响', status: '已完成' },
  { id: 4, name: '极端温度情景', description: '模拟极端温度变化对大坝结构的影响', status: '未开始' }
])

// 当前选中的场景
const currentScenario = ref(scenarios.value[0])

// 模拟参数
const simulationParams = reactive({
  duration: 72,
  intensity: 85,
  waterLevel: 150,
  flowRate: 2000
})

// 模拟结果
const simulationResults = ref({
  safetyIndex: 87,
  riskLevel: '中等',
  criticalPoints: [
    { name: '溢洪道', status: '正常', value: '1850 m³/s' },
    { name: '坝体结构', status: '正常', value: '位移 <2mm' },
    { name: '下游河道', status: '警告', value: '水位上涨 2.5m' }
  ],
  recommendations: [
    '提前开启泄洪闸门，控制水位上涨速度',
    '加强下游河道监测，确保防洪安全',
    '检查加固坝体薄弱环节，提高抗洪能力'
  ]
})

// 对话框控制
const showParamsDialog = ref(false)
const showResultsDialog = ref(false)

// 打开参数设置对话框
const openParamsDialog = () => {
  showParamsDialog.value = true
}

// 打开结果详情对话框
const openResultsDialog = () => {
  showResultsDialog.value = true
}

// 关闭对话框
const handleClose = () => {
  showParamsDialog.value = false
  showResultsDialog.value = false
}

// 选择场景
const selectScenario = (scenario) => {
  currentScenario.value = scenario
}

// 开始模拟
const startSimulation = () => {
  handleClose()
  // 这里应该有实际的模拟逻辑
  setTimeout(() => {
    openResultsDialog()
  }, 1000)
}

// 辅助函数
const getStatusType = (status) => {
  if (status === '已完成') return 'success'
  if (status === '进行中') return 'primary'
  return 'info'
}

const getSafetyColor = (score) => {
  if (score >= 90) return '#67C23A'
  if (score >= 80) return '#E6A23C'
  return '#F56C6C'
}

const getRiskClass = (risk) => {
  if (risk === '低') return 'low-risk'
  if (risk === '中等') return 'medium-risk'
  return 'high-risk'
}

const getPointStatusType = (status) => {
  if (status === '正常') return 'success'
  if (status === '警告') return 'warning'
  return 'danger'
}
</script>

<template>
  <AppLayout>
    <div class="simulation-container fade-in">
      <div class="page-header slide-in-left">
        <h1>预演分析</h1>
        <el-button type="primary" @click="openParamsDialog" class="btn-modern">新建模拟</el-button>
      </div>
      
      <div class="scenarios-section slide-in-right delay-300">
        <el-card class="card-hover">
          <template #header>
            <div class="card-header">
              <span>模拟场景</span>
            </div>
          </template>
          <div class="scenarios-list">
            <el-table :data="scenarios" style="width: 100%" @row-click="selectScenario">
              <el-table-column prop="name" label="场景名称" width="180" />
              <el-table-column prop="description" label="场景描述" />
              <el-table-column prop="status" label="状态" width="120">
                <template #default="scope">
                  <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200">
                <template #default="scope">
                  <el-button type="text" @click="selectScenario(scope.row); openResultsDialog()" 
                    :disabled="scope.row.status === '未开始'">查看结果</el-button>
                  <el-button type="text" @click="selectScenario(scope.row); openParamsDialog()">编辑参数</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </div>
      
      <div class="current-scenario-section slide-in-left delay-400" v-if="currentScenario">
        <el-card class="card-hover">
          <template #header>
            <div class="card-header">
              <span>当前场景: {{ currentScenario.name }}</span>
            </div>
          </template>
          <div class="scenario-details">
            <div class="scenario-info">
              <h3>场景信息</h3>
              <p><strong>描述:</strong> {{ currentScenario.description }}</p>
              <p><strong>状态:</strong> {{ currentScenario.status }}</p>
              <p><strong>模拟时长:</strong> {{ simulationParams.duration }} 小时</p>
              <p><strong>模拟强度:</strong> {{ simulationParams.intensity }}%</p>
            </div>
            
            <div class="scenario-actions">
              <el-button type="primary" @click="openParamsDialog">修改参数</el-button>
              <el-button type="success" @click="startSimulation" 
                :disabled="currentScenario.status === '进行中'">开始模拟</el-button>
              <el-button type="info" @click="openResultsDialog"
                :disabled="currentScenario.status === '未开始'">查看结果</el-button>
            </div>
          </div>
        </el-card>
      </div>
    </div>
    
    <!-- 参数设置对话框 -->
    <el-dialog
      v-model="showParamsDialog"
      title="模拟参数设置"
      :width="paramsDialogWidth"
    >
      <div class="params-form">
        <el-form label-width="120px">
          <el-form-item label="场景名称">
            <el-input v-model="currentScenario.name" />
          </el-form-item>
          <el-form-item label="场景描述">
            <el-input v-model="currentScenario.description" type="textarea" />
          </el-form-item>
          <el-form-item label="模拟时长(小时)">
            <el-slider v-model="simulationParams.duration" :min="24" :max="168" :step="12" show-input />
          </el-form-item>
          <el-form-item label="模拟强度(%)">
            <el-slider v-model="simulationParams.intensity" :min="50" :max="100" :step="5" show-input />
          </el-form-item>
          <el-form-item label="水位高度(m)">
            <el-input-number v-model="simulationParams.waterLevel" :min="100" :max="200" :step="5" />
          </el-form-item>
          <el-form-item label="流量(m³/s)">
            <el-input-number v-model="simulationParams.flowRate" :min="500" :max="5000" :step="100" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="startSimulation">开始模拟</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 结果详情对话框 -->
    <el-dialog
      v-model="showResultsDialog"
      title="模拟结果分析"
      :width="resultsDialogWidth"
    >
      <div class="results-content">
        <h2>{{ currentScenario.name }} - 模拟结果</h2>
        
        <div class="results-summary">
          <div class="result-item">
            <div class="result-label">安全指数</div>
            <div class="result-value">{{ simulationResults.safetyIndex }}</div>
            <el-progress :percentage="simulationResults.safetyIndex" :color="getSafetyColor(simulationResults.safetyIndex)"></el-progress>
          </div>
          <div class="result-item">
            <div class="result-label">风险等级</div>
            <div class="result-value" :class="getRiskClass(simulationResults.riskLevel)">
              {{ simulationResults.riskLevel }}
            </div>
          </div>
        </div>
        
        <div class="critical-points">
          <h3>关键点位状态</h3>
          <el-table :data="simulationResults.criticalPoints" style="width: 100%">
            <el-table-column prop="name" label="监测点位" />
            <el-table-column prop="status" label="状态">
              <template #default="scope">
                <el-tag :type="getPointStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="value" label="监测值" />
          </el-table>
        </div>
        
        <div class="recommendations">
          <h3>应对建议</h3>
          <el-card>
            <ul class="recommendation-list">
              <li v-for="(rec, index) in simulationResults.recommendations" :key="index">
                {{ rec }}
              </li>
            </ul>
          </el-card>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">关闭</el-button>
          <el-button type="primary">导出报告</el-button>
        </span>
      </template>
    </el-dialog>
  </AppLayout>
</template>

<style scoped>
.simulation-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

h1 {
  margin: 0;
  color: #303133;
}

.scenarios-section {
  margin-bottom: 30px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-scenario-section {
  margin-bottom: 30px;
}

.scenario-details {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.scenario-info {
  flex: 1;
}

.scenario-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
  min-width: 150px;
}

.params-form {
  padding: 20px 0;
}

.results-content {
  padding: 20px 0;
}

.results-summary {
  display: flex;
  gap: 40px;
  margin-bottom: 30px;
}

.result-item {
  flex: 1;
  text-align: center;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.result-label {
  font-size: 16px;
  margin-bottom: 10px;
  color: #606266;
}

.result-value {
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #409EFF;
}

.low-risk {
  color: #67C23A;
}

.medium-risk {
  color: #E6A23C;
}

.high-risk {
  color: #F56C6C;
}

.critical-points, .recommendations {
  margin-bottom: 30px;
}

h2, h3 {
  border-bottom: 1px solid #EBEEF5;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

.recommendation-list {
  padding-left: 20px;
  margin: 0;
}

.recommendation-list li {
  margin-bottom: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .scenario-details {
    flex-direction: column;
    gap: 20px;
  }

  .scenario-actions {
    flex-direction: row;
    flex-wrap: wrap;
    width: 100%;
  }

  .results-summary {
    flex-direction: column;
  }
}
</style> 