<script setup lang="ts">
import { RouterView } from 'vue-router'
</script>

<template>
  <div class="app-container">
    <RouterView />
  </div>
</template>

<style>
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

#app {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.app-container {
  width: 1920px;
  height: 1080px;
  margin: 0 auto;
  background: #F0F1F3;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  overflow: hidden;
}

@media screen and (max-width: 1920px), screen and (max-height: 1080px) {
  .app-container {
    width: 100%;
    height: 100%;
    transform: none;
    top: 0;
    left: 0;
  }
}
</style>
