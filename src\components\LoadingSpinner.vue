<template>
  <div class="loading-container" :class="{ 'full-screen': fullScreen }">
    <div class="loading-content">
      <div class="spinner-wrapper">
        <div class="spinner" :class="spinnerType">
          <div v-if="spinnerType === 'dots'" class="dots">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
          </div>
          <div v-else-if="spinnerType === 'circle'" class="circle">
            <div class="circle-inner"></div>
          </div>
          <div v-else-if="spinnerType === 'pulse'" class="pulse">
            <div class="pulse-ring"></div>
            <div class="pulse-ring"></div>
            <div class="pulse-ring"></div>
          </div>
        </div>
      </div>
      <div v-if="text" class="loading-text">{{ text }}</div>
      <div v-if="progress !== null" class="progress-container">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: progress + '%' }"></div>
        </div>
        <div class="progress-text">{{ Math.round(progress) }}%</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  fullScreen?: boolean
  text?: string
  spinnerType?: 'dots' | 'circle' | 'pulse'
  progress?: number | null
}

withDefaults(defineProps<Props>(), {
  fullScreen: false,
  text: '加载中...',
  spinnerType: 'circle',
  progress: null
})
</script>

<style scoped>
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.loading-container.full-screen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  z-index: 9999;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.spinner-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 圆形加载器 */
.spinner.circle {
  width: 40px;
  height: 40px;
  position: relative;
}

.circle-inner {
  width: 100%;
  height: 100%;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #4797C8;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 点状加载器 */
.spinner.dots {
  display: flex;
  gap: 4px;
}

.dot {
  width: 8px;
  height: 8px;
  background-color: #4797C8;
  border-radius: 50%;
  animation: bounce 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* 脉冲加载器 */
.spinner.pulse {
  position: relative;
  width: 40px;
  height: 40px;
}

.pulse-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 2px solid #4797C8;
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.pulse-ring:nth-child(2) {
  animation-delay: 0.5s;
}

.pulse-ring:nth-child(3) {
  animation-delay: 1s;
}

@keyframes pulse {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

/* 加载文本 */
.loading-text {
  font-family: "Microsoft YaHei", sans-serif;
  font-size: 14px;
  color: #666;
  text-align: center;
  animation: fadeInOut 2s ease-in-out infinite;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* 进度条 */
.progress-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  width: 200px;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background-color: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4797C8, #5ba3d4);
  border-radius: 2px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-image: linear-gradient(
    -45deg,
    rgba(255, 255, 255, 0.2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 75%,
    transparent
  );
  background-size: 20px 20px;
  animation: move 1s linear infinite;
}

@keyframes move {
  0% { background-position: 0 0; }
  100% { background-position: 20px 20px; }
}

.progress-text {
  font-family: "Microsoft YaHei", sans-serif;
  font-size: 12px;
  color: #666;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loading-content {
    gap: 12px;
  }
  
  .progress-container {
    width: 150px;
  }
  
  .loading-text {
    font-size: 12px;
  }
}
</style>
