<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import menuIcon from '@/assets/menu-icon.svg'
import settingsIcon from '@/assets/settings-icon.svg'
import powerSwitchIcon from '@/assets/power-switch-icon.svg'

const router = useRouter()
const activeMenu = ref(router.currentRoute.value.path)
const isMobileMenuOpen = ref(false)

const navigateTo = (path: string) => {
  router.push(path)
  activeMenu.value = path
  isMobileMenuOpen.value = false
}

const goToHome = () => {
  router.push('/')
  activeMenu.value = '/'
  isMobileMenuOpen.value = false
}

const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}
</script>

<template>
  <div class="layout-container">
    <div class="header">
      <div class="logo-section" @click="goToHome">
        <div class="menu-icon">
          <img :src="menuIcon" alt="Menu" />
        </div>
        <div class="logo-text">大坝安全</div>
      </div>
      <div class="nav-menu">
        <div 
          class="nav-item" 
          :class="{ active: activeMenu === '/comprehensive-evaluation' }" 
          @click="navigateTo('/comprehensive-evaluation')"
        >
          综合评价
        </div>
        <div 
          class="nav-item" 
          :class="{ active: activeMenu === '/simulation-analysis' }" 
          @click="navigateTo('/simulation-analysis')"
        >
          预演分析
        </div>
        <div 
          class="nav-item" 
          :class="{ active: activeMenu === '/warning-indicators' }" 
          @click="navigateTo('/warning-indicators')"
        >
          预警指标
        </div>
        <div 
          class="nav-item" 
          :class="{ active: activeMenu === '/monitoring-model' }" 
          @click="navigateTo('/monitoring-model')"
        >
          监控模型
        </div>
      </div>
      <div class="user-section">
        <div class="user-avatar"></div>
        <span class="user-text">管理员</span>
        <div class="divider-line"></div>
        <img :src="settingsIcon" alt="设置" class="header-icon" />
        <img :src="powerSwitchIcon" alt="开关" class="header-icon" />
      </div>
       <div class="mobile-menu-icon" @click="toggleMobileMenu">
        <img :src="menuIcon" alt="Menu" />
      </div>
    </div>
    <transition name="slide">
      <div v-if="isMobileMenuOpen" class="mobile-nav-menu">
         <div 
          class="nav-item" 
          :class="{ active: activeMenu === '/comprehensive-evaluation' }" 
          @click="navigateTo('/comprehensive-evaluation')"
        >
          综合评价
        </div>
        <div 
          class="nav-item" 
          :class="{ active: activeMenu === '/simulation-analysis' }" 
          @click="navigateTo('/simulation-analysis')"
        >
          预演分析
        </div>
        <div 
          class="nav-item" 
          :class="{ active: activeMenu === '/warning-indicators' }" 
          @click="navigateTo('/warning-indicators')"
        >
          预警指标
        </div>
        <div 
          class="nav-item" 
          :class="{ active: activeMenu === '/monitoring-model' }" 
          @click="navigateTo('/monitoring-model')"
        >
          监控模型
        </div>
      </div>
    </transition>
    <div class="main-content">
      <div class="content">
        <transition name="page-transition" mode="out-in">
          <slot></slot>
        </transition>
      </div>
    </div>
  </div>
</template>

<style scoped>
.layout-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.header {
  width: 100%;
  max-width: 1920px;
  /* min-width: 1200px; */
  height: 60px;
  background: #354F74;
  display: flex;
  align-items: center;
  color: white;
  flex-shrink: 0;
  justify-content: space-between;
  position: relative;
}

.logo-section {
  width: 280px;
  height: 60px;
  background: #3A567E;
  display: flex;
  align-items: center;
  padding: 0 20px;
  cursor: pointer;
  flex-shrink: 0;
}

.menu-icon {
  width: 20px;
  height: 19px;
  border-radius: 12px;
  margin-right: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.logo-text {
  width: 96px;
  height: 32px;
  font-family: "Microsoft YaHei";
  font-size: 24px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #FFFFFF;
  display: flex;
  align-items: center;
}

.nav-menu {
  display: flex;
  height: 100%;
  flex: 1;
  margin-left: 20px;
  overflow-x: auto;
  overflow-y: hidden;
}

.nav-item {
  width: 132px;
  height: 60px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

.nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.nav-item:hover::before {
  left: 100%;
}

.nav-item:hover {
  background-color: rgba(71, 151, 200, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.nav-item.active {
  background-color: #4797C8;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-section {
  display: flex;
  align-items: center;
  gap: 15px;
  padding-right: 20px;
  flex-shrink: 0;
  min-width: 150px;
}

.user-avatar {
  width: 28px;
  height: 28px;
  background-color: #FFFFFF;
  border-radius: 50%;
  flex-shrink: 0;
}

.user-text {
  font-size: 16px;
  color: #FFFFFF;
  white-space: nowrap;
}

.divider-line {
  width: 18px;
  height: 0px;
  transform: rotate(90deg);
  border-top: 2px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
}

.header-icon {
  width: 20px;
  height: 20px;
  cursor: pointer;
  flex-shrink: 0;
}

.mobile-menu-icon {
  display: none;
  cursor: pointer;
  padding: 0 20px;
}

.mobile-nav-menu {
  display: none;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content {
  flex: 1;
  padding: 20px;
  overflow: auto;
  background: #F0F1F3;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from,
.slide-leave-to {
  transform: translateX(-100%);
}


@media (max-width: 768px) {
  .header {
    justify-content: space-between;
  }

  .logo-section {
    width: auto;
    background: transparent;
  }

  .nav-menu {
    display: none;
  }

  .mobile-menu-icon {
    display: flex;
    align-items: center;
  }
  
  .user-text, .divider-line {
    display: none;
  }

  .mobile-nav-menu {
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 60px;
    left: 0;
    width: 250px;
    height: calc(100% - 60px);
    background: #354F74;
    padding-top: 20px;
    z-index: 1000;
  }

  .mobile-nav-menu .nav-item {
    width: 100%;
    justify-content: flex-start;
    padding: 15px 20px;
  }
}
</style> 