<script setup lang="ts">
import AppLayout from '@/components/AppLayout.vue'
import DamModel3D from '@/components/DamModel3D.vue' // Import the new component
import CountUp from '@/components/CountUp.vue'
import { ref, onMounted, computed } from 'vue'
import alertIcon from '@/assets/alert-icon.svg'
import waterLevelIcon from '/resource/坝前水位icon.svg'
import leakageIcon from '/resource/大坝渗漏量icon.svg'
import displacementIcon from '/resource/大坝位移量icon.svg'
import settlementIcon from '/resource/大坝沉降量icon.svg'
import * as echarts from 'echarts'

const selectedProject = ref('')
const waterLevel = ref('23.2')

// 表格相关数据
const alarmHistory = ref([
  { id: 1, time: '2025-06-20', point: 'TPX-01', location: '部位部位', message: 'XXX测值异常' },
  { id: 2, time: '2025-06-20', point: 'TPX-01', location: '部位部位', message: 'XXX测值异常' },
  { id: 3, time: '2025-06-20', point: 'TPX-01', location: '部位部位', message: 'XXX测值异常' },
  { id: 4, time: '2025-06-20', point: 'TPX-01', location: '部位部位', message: 'XXX测值异常' },
  { id: 5, time: '2025-06-20', point: 'TPX-01', location: '部位部位', message: 'XXX测值异常' },
  { id: 6, time: '2025-06-20', point: 'TPX-01', location: '部位部位', message: 'XXX测值异常' },
  { id: 7, time: '2025-06-20', point: 'TPX-01', location: '部位部位', message: 'XXX测值异常' }
])

// 分页相关变量
const currentPage = ref(1)
const pageSize = ref(3)
const pageSizeOptions = [3, 5, 10, 20]

// 计算属性：当前页数据
const currentPageData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return alarmHistory.value.slice(start, end)
})

// 计算属性：总页数
const totalPages = computed(() => {
  return Math.ceil(alarmHistory.value.length / pageSize.value)
})

// 分页方法
const changePage = (page: number) => {
  if (page < 1 || page > totalPages.value) return
  currentPage.value = page
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

// 改变每页条数
const changePageSize = (event: Event) => {
  const select = event.target as HTMLSelectElement
  pageSize.value = parseInt(select.value)
  currentPage.value = 1 // 重置为第一页
}

// 表格加载状态
const loading = ref(false)

const showAlertInfo = () => {
  console.log('显示报警信息')
  // 这里可以添加显示报警信息的逻辑
}

onMounted(() => {
  initLeftRadarChart()
  initRightRadarChart()
})

// 初始化左侧雷达图
const initLeftRadarChart = () => {
  const chartDom = document.getElementById('leftRadarChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  
  const option = {
    animation: true,
    animationDuration: 2000,
    animationEasing: 'cubicOut',
    radar: {
      indicator: [
        { name: '', max: 100 },
        { name: '', max: 100 },
        { name: '', max: 100 },
        { name: '', max: 100 }
      ],
      radius: 70,
      center: ['50%', '50%'],
      shape: 'circle',
      splitNumber: 3,
      axisName: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#E1E4E9',
          width: 1
        }
      },
      splitArea: {
        show: true,
        areaStyle: {
          color: ['rgba(71, 151, 200, 0.08)', 'rgba(71, 151, 200, 0.04)', 'rgba(71, 151, 200, 0.02)']
        }
      },
      axisLine: {
        lineStyle: {
          color: '#E1E4E9',
          width: 1
        }
      }
    },
    series: [
      {
        type: 'radar',
        data: [
          {
            value: [70, 65, 80, 75],
            name: '安全指标',
            areaStyle: {
              color: {
                type: 'radial',
                x: 0.5,
                y: 0.5,
                r: 0.5,
                colorStops: [
                  { offset: 0, color: 'rgba(71, 151, 200, 0.5)' },
                  { offset: 1, color: 'rgba(71, 151, 200, 0.1)' }
                ]
              }
            },
            lineStyle: {
              color: '#4797C8',
              width: 3,
              shadowColor: 'rgba(71, 151, 200, 0.3)',
              shadowBlur: 8
            },
            itemStyle: {
              color: '#4797C8',
              borderColor: '#ffffff',
              borderWidth: 2,
              shadowColor: 'rgba(71, 151, 200, 0.4)',
              shadowBlur: 6
            },
            symbol: 'circle',
            symbolSize: 8
          }
        ],
        animationDelay: 300
      }
    ]
  }
  
  myChart.setOption(option)
  
  window.addEventListener('resize', () => {
    myChart.resize()
  })
}

// 初始化右侧雷达图
const initRightRadarChart = () => {
  const chartDom = document.getElementById('rightRadarChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  
  const option = {
    radar: {
      indicator: [
        { name: '', max: 100 },
        { name: '', max: 100 },
        { name: '', max: 100 },
        { name: '', max: 100 }
      ],
      radius: 70,
      center: ['50%', '50%'],
      shape: 'circle',
      splitNumber: 3,
      axisName: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#E1E4E9'
        }
      },
      splitArea: {
        show: true,
        areaStyle: {
          color: ['#F5F7FA', '#F0F2F5', '#E6EBF5']
        }
      },
      axisLine: {
        lineStyle: {
          color: '#E1E4E9'
        }
      }
    },
    series: [
      {
        type: 'radar',
        data: [
          {
            value: [75, 70, 65, 80],
            name: '安全指标',
            areaStyle: {
              color: 'rgba(71, 151, 200, 0.4)'
            },
            lineStyle: {
              color: '#4797C8'
            },
            itemStyle: {
              color: '#4797C8'
            }
          }
        ]
      }
    ]
  }
  
  myChart.setOption(option)
  
  window.addEventListener('resize', () => {
    myChart.resize()
  })
}
</script>

<template>
  <AppLayout>
    <div class="page-container fade-in">
      <div class="page-title slide-in-left">大坝安全</div>
      <div class="dam-image-container slide-in-right delay-200">
        <div class="select-container bounce-in delay-300">
          <select v-model="selectedProject" class="project-select">
            <option value="" disabled selected>请选择监测项目</option>
            <option value="project1">项目一</option>
            <option value="project2">项目二</option>
            <option value="project3">项目三</option>
          </select>
          <div class="water-level-display">
            <span class="water-level-label">当前库水位</span>
            <CountUp :end-value="parseFloat(waterLevel)" :decimals="1" class="water-level-number" /><span class="water-level-unit">m</span>
          </div>
          <div class="alert-display" @click="showAlertInfo">
            <img :src="alertIcon" alt="报警" class="alert-icon" />
            <span class="alert-text">暂无报警信息</span>
          </div>
        </div>
        
        <!-- Use the reusable 3D model component here -->
        <DamModel3D class="dam-container-wrapper" />

        <div class="right-panel slide-in-right delay-400">
          <div class="panel-title fade-in delay-500">综合评判</div>
          <div class="radar-charts">
            <div class="radar-chart-container">
              <div class="radar-label radar-label-top">主坝渗流</div>
              <div class="radar-label radar-label-right">绕坝<br>渗流</div>
              <div class="radar-label radar-label-bottom">主坝渗流量</div>
              <div class="radar-label radar-label-left">泄洪闸<br>场压力</div>
              <div id="leftRadarChart" class="radar-chart"></div>
              <div class="radar-status-box">
                <div class="status-text">渗流综合评判</div>
                <div class="status-normal">
                  <span class="status-icon"></span>
                  <span>正常</span>
                </div>
              </div>
            </div>
            <div class="radar-chart-container">
              <div class="radar-label radar-label-top">主坝水平位移</div>
              <div class="radar-label radar-label-right">泄洪闸<br>沉降</div>
              <div class="radar-label radar-label-bottom">泄洪闸水平位移</div>
              <div class="radar-label radar-label-left">主坝<br>沉降</div>
              <div id="rightRadarChart" class="radar-chart"></div>
              <div class="radar-status-box">
                <div class="status-text">变形综合评判</div>
                <div class="status-normal">
                  <span class="status-icon"></span>
                  <span>正常</span>
                </div>
              </div>
            </div>
          </div>
          <div class="panel-title key-indicators-title">大坝安全关键指标</div>
          <div class="indicators-container">
            <div class="water-history-box card-hover">
              <div class="water-history-icon">
                <img :src="waterLevelIcon" alt="水位" />
              </div>
              <div class="water-history-content">
                <div class="water-history-title">坝前水位历史最大</div>
                <div class="water-history-data">
                  <div class="water-history-value">
                    <CountUp :end-value="23.2" :decimals="1" class="water-history-number" />
                    <span class="water-history-unit">m</span>
                  </div>
                  <div class="water-history-date">2025-06-20</div>
                </div>
              </div>
            </div>
            <div class="water-history-box card-hover">
              <div class="water-history-icon">
                <img :src="leakageIcon" alt="渗漏量" />
              </div>
              <div class="water-history-content">
                <div class="water-history-title">大坝渗漏量历史最大</div>
                <div class="water-history-data">
                  <div class="water-history-value">
                    <CountUp :end-value="23.2" :decimals="1" class="water-history-number" />
                    <span class="water-history-unit">m</span>
                  </div>
                  <div class="water-history-date">2025-06-20</div>
                </div>
              </div>
            </div>
          </div>
          <div class="indicators-row">
            <div class="displacement-box card-hover">
              <div class="displacement-header">
                <div class="water-history-icon">
                  <img :src="displacementIcon" alt="位移量" />
                </div>
                <div class="water-history-content">
                  <div class="water-history-title">大坝位移量累计</div>
                  <div class="water-history-data">
                    <div class="water-history-value">
                      <CountUp :end-value="23.2" :decimals="1" class="water-history-number" />
                      <span class="water-history-unit">m</span>
                    </div>
                    <div class="water-history-date">2025-06-20</div>
                  </div>
                </div>
              </div>
              <div class="dashed-divider"></div>
              <div class="displacement-footer">
                <div class="history-label">历史最大</div>
                <div class="history-data">
                  <div class="water-history-value">
                    <CountUp :end-value="23.2" :decimals="1" class="water-history-number" />
                    <span class="water-history-unit">m</span>
                  </div>
                  <div class="water-history-date">2025-06-20</div>
                </div>
              </div>
            </div>
            <div class="displacement-box card-hover">
              <div class="displacement-header">
                <div class="water-history-icon">
                  <img :src="settlementIcon" alt="沉降量" />
                </div>
                <div class="water-history-content">
                  <div class="water-history-title">大坝沉降量累计</div>
                  <div class="water-history-data">
                    <div class="water-history-value">
                      <CountUp :end-value="23.2" :decimals="1" class="water-history-number" />
                      <span class="water-history-unit">m</span>
                    </div>
                    <div class="water-history-date">2025-06-20</div>
                  </div>
                </div>
              </div>
              <div class="dashed-divider"></div>
              <div class="displacement-footer">
                <div class="history-label">历史最大</div>
                <div class="history-data">
                  <div class="water-history-value">
                    <CountUp :end-value="23.2" :decimals="1" class="water-history-number" />
                    <span class="water-history-unit">m</span>
                  </div>
                  <div class="water-history-date">2025-06-20</div>
                </div>
              </div>
            </div>
          </div>
          <div class="panel-title history-alarm-title">历史报警</div>
          <div class="alarm-table-container">
            <div class="table-wrapper">
              <table>
                <thead>
                  <tr>
                    <th>序号</th>
                    <th>时间</th>
                    <th>测点</th>
                    <th>工程部位</th>
                    <th>预警信息</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-if="loading">
                    <td colspan="5" class="text-center">数据加载中...</td>
                  </tr>
                  <tr v-else-if="alarmHistory.length === 0">
                    <td colspan="5" class="text-center">暂无数据</td>
                  </tr>
                  <template v-else>
                    <tr v-for="item in currentPageData" :key="item.id" class="table-row-hover">
                      <td>{{ item.id }}</td>
                      <td>{{ item.time }}</td>
                      <td>{{ item.point }}</td>
                      <td>{{ item.location }}</td>
                      <td>{{ item.message }}</td>
                    </tr>
                  </template>
                </tbody>
              </table>
            </div>
            <div class="table-footer">
              <div class="total-records">共{{ alarmHistory.length }}条记录</div>
              <div class="pagination">
                <div class="pagination-controls">
                  <select class="page-size-select" :value="pageSize" @change="changePageSize">
                    <option v-for="size in pageSizeOptions" :key="size" :value="size">{{ size }}条/页</option>
                  </select>
                  <button class="page-nav" @click="prevPage" :disabled="currentPage <= 1"><</button>
                  <template v-if="totalPages <= 5">
                    <button 
                      v-for="page in totalPages" 
                      :key="page" 
                      class="page-num" 
                      :class="{ active: page === currentPage }"
                      @click="changePage(page)"
                    >{{ page }}</button>
                  </template>
                  <template v-else>
                    <button 
                      v-if="currentPage > 3"
                      class="page-num" 
                      @click="changePage(1)"
                    >1</button>
                    <span v-if="currentPage > 4" class="page-ellipsis">...</span>
                    <template v-for="i in 3" :key="i">
                      <button
                        v-if="currentPage - 2 + i > 0 && currentPage - 2 + i <= totalPages"
                        class="page-num"
                        :class="{ active: currentPage - 2 + i === currentPage }"
                        @click="changePage(currentPage - 2 + i)"
                      >{{ currentPage - 2 + i }}</button>
                    </template>
                    <span v-if="currentPage < totalPages - 3" class="page-ellipsis">...</span>
                    <button 
                      v-if="currentPage < totalPages - 2"
                      class="page-num" 
                      @click="changePage(totalPages)"
                    >{{ totalPages }}</button>
                  </template>
                  <button class="page-nav" @click="nextPage" :disabled="currentPage >= totalPages">></button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<style scoped>
.page-container {
  display: flex;
  flex-direction: column;
}

.page-title {
  width: 56px;
  height: 19px;
  margin-top: 0px;
  margin-left: 0px;
  font-family: "Microsoft YaHei";
  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #333333;
}

.dam-image-container {
  position: relative;
  margin-top: 10px;
  display: flex;
  box-shadow: 0px 0px 16px 0px rgba(18, 30, 46, 0.08);
  border-radius: 4px;
}

.select-container {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
  display: flex;
  gap: 10px;
}

.project-select {
  width: 240px;
  height: 36px;
  border-radius: 2px;
  background: #FFFFFF;
  box-sizing: border-box;
  border: 1px solid #E1E4E9;
  padding: 0 10px;
  font-family: "Microsoft YaHei";
  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #BFBFBF;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' width='14' height='14' viewBox='0 0 24 24' fill='none' stroke='%23BFBFBF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 10px center;
}

.project-select option {
  color: #333333;
}

.project-select option:first-child {
  color: #BFBFBF;
}

.water-level-display {
  width: 240px;
  height: 36px;
  border-radius: 2px;
  background: #FFFFFF;
  box-sizing: border-box;
  border: 1px solid #E1E4E9;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
}

.water-level-label {
  font-family: "Microsoft YaHei";
  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #555555;
  margin-right: 8px;
}

.water-level-value {
  font-family: "Microsoft YaHei";
  font-weight: 600;
  line-height: normal;
  letter-spacing: normal;
  color: #333333;
}

.water-level-number {
  font-family: "Microsoft YaHei";
  font-weight: 600;
  font-size: 18px;
  font-variation-settings: "opsz" auto;
}

.water-level-unit {
  font-family: "Microsoft YaHei";
  font-weight: 600;
  font-size: 14px;
  font-variation-settings: "opsz" auto;
}

.alert-display {
  width: 240px;
  height: 36px;
  border-radius: 2px;
  background: #FFFFFF;
  box-sizing: border-box;
  border: 1px solid #E1E4E9;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  cursor: pointer;
}

.alert-icon {
  width: 18px;
  height: 18px;
  margin-right: 10px;
}

.alert-text {
  font-family: "Microsoft YaHei";
  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #555555;
}

/* This class ensures the component fits into the flex layout correctly */
.dam-container-wrapper {
  width: calc(100% - 480px);
  height: 964px;
  flex-shrink: 0;
}

.right-panel {
  width: 480px;
  height: 964px;
  border-radius: 0px 4px 4px 0px;
  background: #FFFFFF;
  box-shadow: none;
  flex-shrink: 0;
  padding: 20px;
  box-sizing: border-box;
  border: 1px solid #e6e6e6;
  border-left: none;
}

.panel-title {
  width: 72px;
  height: 24px;
  font-family: "Microsoft YaHei";
  font-size: 18px;
  font-weight: 600;
  line-height: normal;
  letter-spacing: normal;
  color: #333333;
  margin-bottom: 20px;
}

.radar-charts {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-top: 20px;
}

.radar-chart-container {
  width: 240px;
  height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.radar-chart {
  width: 140px;
  height: 140px;
}

.radar-status-box {
  width: 216px;
  height: 40px;
  border-radius: 4px;
  background: linear-gradient(270deg, rgba(71, 151, 200, 0) 0%, rgba(71, 151, 200, 0.1) 100%);
  box-sizing: border-box;
  border: 1px solid rgba(71, 151, 200, 0.3);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  position: absolute;
  bottom: -50px;
}

.status-text {
  width: 84px;
  height: 19px;
  font-family: Microsoft YaHei;
  font-size: 14px;
  font-weight: 600;
  line-height: normal;
  text-align: center;
  letter-spacing: normal;
  color: #333333;
}

.status-normal {
  display: flex;
  align-items: center;
}

.status-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background-color: #4FCA89;
  border-radius: 50%;
  position: relative;
  margin-right: 5px;
}

.status-icon::after {
  content: "";
  position: absolute;
  top: 6px;
  left: 5px;
  width: 10px;
  height: 6px;
  border-left: 2px solid white;
  border-bottom: 2px solid white;
  transform: rotate(-45deg);
}

.status-normal span:last-child {
  width: 28px;
  height: 19px;
  font-family: Microsoft YaHei;
  font-size: 14px;
  font-weight: 600;
  line-height: normal;
  letter-spacing: normal;
  color: #4FCA89;
}

.key-indicators-title {
  width: auto;
  margin-top: 80px;
}

.indicators-container {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  gap: 20px;
}

.indicators-row {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  gap: 20px;
}

.history-alarm-title {
  margin-top: 20px;
}

.alarm-table-container {
  margin-top: 20px;
  max-width: 100%;
  overflow: hidden;
}

.table-wrapper {
  overflow-x: auto;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  max-height: 220px; /* 固定表格容器高度 */
}

/* 自定义滚动条样式 */
.table-wrapper::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.table-wrapper::-webkit-scrollbar-thumb {
  width: 6px;
  height: 70px;
  border-radius: 10px;
  background: #E8E8E8;
}

.table-wrapper::-webkit-scrollbar-track {
  background: transparent;
}

tbody::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

tbody::-webkit-scrollbar-thumb {
  width: 6px;
  height: 70px;
  border-radius: 10px;
  background: #E8E8E8;
}

tbody::-webkit-scrollbar-track {
  background: transparent;
}

table {
  width: 100%;
  border-collapse: collapse;
  white-space: nowrap;
  border: 1px solid #DCDFE6; /* 加粗表格外边框 */
}

thead {
  background-color: #F5F7FA;
  position: sticky; /* 固定表头 */
  top: 0;
  z-index: 1;
}

tbody {
  overflow-y: auto; /* 表格体可滚动 */
}

th, td {
  padding: 12px;
  text-align: left;
  border-bottom: 2px solid #DCDFE6; /* 加粗横线 */
  border-right: 2px solid #DCDFE6; /* 加粗竖线 */
  font-family: "Microsoft YaHei";
  font-size: 14px;
  color: #606266;
}

th:last-child, td:last-child {
  border-right: none; /* 最后一列不需要右边框 */
}

th {
  font-weight: 600;
  color: #333333;
  border-bottom: 2px solid #DCDFE6; /* 表头底部边框加粗 */
}

tbody tr:hover {
  background-color: #F5F7FA;
}

.table-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.total-records {
  font-family: "Microsoft YaHei";
  font-size: 14px;
  color: #606266;
}

.pagination {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-size-select {
  width: 100px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #DCDFE6;
  padding: 0 8px;
  font-size: 14px;
  color: #606266;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%23606266' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 8px center;
}

.page-nav, .page-num {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #DCDFE6;
  background-color: white;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-nav:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.page-num.active {
  background-color: #409EFF;
  color: white;
  border-color: #409EFF;
}

.page-ellipsis {
  display: inline-block;
  width: 24px;
  text-align: center;
  line-height: 32px;
}

.text-center {
  text-align: center;
}

.displacement-box {
  width: 216px;
  height: 132px;
  border-radius: 4px;
  background: linear-gradient(270deg, rgba(71, 151, 200, 0) 0%, rgba(71, 151, 200, 0.1) 100%);
  box-sizing: border-box;
  border: 1px solid rgba(71, 151, 200, 0.3);
  padding: 10px;
  padding-left: 5px;
  display: flex;
  flex-direction: column;
}

.displacement-header {
  display: flex;
  align-items: center;
  height: 70px;
}

.dashed-divider {
  width: 146px;
  height: 0px;
  border-top: 1px dashed rgba(71, 151, 200, 0.2);
  margin: 0;
  margin-left: 49px;
}

.displacement-footer {
  display: flex;
  flex-direction: column;
  padding: 5px 0;
  margin-left: 49px;
}

.history-label {
  font-family: Microsoft YaHei;
  font-size: 14px;
  font-weight: 600;
  line-height: normal;
  letter-spacing: normal;
  color: #333333;
  white-space: nowrap;
}

.history-data {
  display: flex;
  align-items: center;
  width: 100%;
  margin-top: 5px;
  white-space: nowrap;
}

.water-history-box {
  width: 216px;
  height: 70px;
  border-radius: 4px;
  background: linear-gradient(270deg, rgba(71, 151, 200, 0) 0%, rgba(71, 151, 200, 0.1) 100%);
  box-sizing: border-box;
  border: 1px solid rgba(71, 151, 200, 0.3);
  display: flex;
  align-items: center;
  padding: 10px;
  padding-left: 5px;
  margin-top: 0;
}

.water-history-icon {
  width: 44px;
  height: 47px;
  margin-right: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.water-history-icon img {
  width: 100%;
  height: 100%;
}

.water-history-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  flex-grow: 1;
  width: calc(100% - 57px);
}

.water-history-title {
  font-family: Microsoft YaHei;
  font-size: 14px;
  font-weight: 600;
  line-height: normal;
  letter-spacing: normal;
  color: #333333;
  white-space: nowrap;
}

.water-history-data {
  display: flex;
  align-items: center;
  width: 100%;
  margin-top: 5px;
  white-space: nowrap;
}

.water-history-value {
  display: flex;
  align-items: baseline;
  flex-basis: 50%;
}

.water-history-number {
  font-family: Microsoft YaHei;
  font-weight: 600;
  font-size: 18px;
  font-variation-settings: "opsz" auto;
  color: #333333;
}

.water-history-unit {
  font-family: Microsoft YaHei;
  font-weight: 600;
  font-size: 14px;
  font-variation-settings: "opsz" auto;
  color: #333333;
}

.water-history-date {
  font-family: Microsoft YaHei;
  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: normal;
  color: rgba(102, 102, 102, 0.8483);
  margin-left: auto;
  white-space: nowrap;
}

.radar-label {
  font-family: Microsoft YaHei;
  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  text-align: center;
  letter-spacing: normal;
  color: #555555;
  position: absolute;
  width: 100px;
}

.radar-label-top {
  top: -2px;
  left: 50%;
  transform: translateX(-50%);
}

.radar-label-right {
  top: 50%;
  right: -39px;
  transform: translateY(-50%);
}

.radar-label-bottom {
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
}

.radar-label-left {
  top: 50%;
  left: -39px;
  transform: translateY(-50%);
}

/* 大屏幕适配 */
@media (max-width: 1400px) {
  .dam-container-wrapper {
    width: calc(100% - 420px);
  }

  .right-panel {
    width: 420px;
  }
}

/* 中等屏幕适配 */
@media (max-width: 1200px) {
  .dam-image-container {
    flex-direction: column;
    height: auto;
  }

  .dam-container-wrapper, .right-panel {
    width: 100%;
    height: auto;
    border-left: 1px solid #e6e6e6;
  }

  .dam-container-wrapper {
    height: 500px;
    border-radius: 4px 4px 0 0;
  }

  .right-panel {
    border-radius: 0 0 4px 4px;
    border-top: none;
  }

  .select-container {
    position: relative;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(8px);
    border-radius: 8px;
    margin: 15px;
    padding: 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

/* 平板适配 */
@media (max-width: 1024px) {
  .radar-charts {
    gap: 15px;
  }

  .radar-chart-container {
    width: 200px;
    height: 180px;
  }

  .indicators-container, .indicators-row {
    gap: 15px;
  }

  .water-history-box, .displacement-box {
    width: 180px;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .page-container {
    padding: 0;
  }

  .page-title {
    padding: 15px 20px 10px;
    font-size: 16px;
  }

  .dam-image-container {
    margin-top: 0;
    border-radius: 0;
  }

  .select-container {
    position: static;
    flex-direction: column;
    width: 100%;
    padding: 15px;
    gap: 12px;
    background: rgba(255, 255, 255, 0.98);
    border-bottom: 1px solid #e6e6e6;
    margin: 0;
    border-radius: 0;
  }

  .project-select, .water-level-display, .alert-display {
    width: 100%;
    height: 44px;
    font-size: 16px;
  }

  .radar-charts, .indicators-container, .indicators-row {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .radar-chart-container {
    width: 100%;
    max-width: 300px;
  }

  .right-panel {
    padding: 15px;
  }

  .panel-title {
    font-size: 16px;
    margin-bottom: 15px;
  }

  .dam-container-wrapper {
    height: 350px;
  }

  .water-history-box, .displacement-box {
    width: 100%;
    max-width: 300px;
  }

  .table-footer {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .pagination {
    justify-content: center;
  }

  .pagination-controls {
    flex-wrap: wrap;
    justify-content: center;
    gap: 6px;
  }
}

/* 小屏幕移动端 */
@media (max-width: 480px) {
  .page-title {
    padding: 12px 15px 8px;
    font-size: 14px;
  }

  .select-container {
    padding: 12px;
    gap: 10px;
  }

  .project-select, .water-level-display, .alert-display {
    height: 40px;
    font-size: 14px;
  }

  .right-panel {
    padding: 12px;
  }

  .panel-title {
    font-size: 14px;
    margin-bottom: 12px;
  }

  .dam-container-wrapper {
    height: 280px;
  }

  .radar-chart-container {
    max-width: 250px;
    height: 160px;
  }

  .water-history-box, .displacement-box {
    max-width: 280px;
    padding: 8px;
  }

  .water-history-title, .history-label {
    font-size: 12px;
  }

  .water-history-number {
    font-size: 16px;
  }

  .table-wrapper {
    font-size: 12px;
  }

  th, td {
    padding: 8px 4px;
  }
}
</style>