<template>
  <span class="count-up" :class="{ 'counting': isAnimating }">
    {{ displayValue }}{{ suffix }}
  </span>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'

interface Props {
  endValue: number
  startValue?: number
  duration?: number
  decimals?: number
  suffix?: string
  prefix?: string
  autoStart?: boolean
  useEasing?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  startValue: 0,
  duration: 2000,
  decimals: 0,
  suffix: '',
  prefix: '',
  autoStart: true,
  useEasing: true
})

const displayValue = ref(props.startValue)
const isAnimating = ref(false)

// 缓动函数
const easeOutQuart = (t: number): number => {
  return 1 - Math.pow(1 - t, 4)
}

const animate = () => {
  if (isAnimating.value) return
  
  isAnimating.value = true
  const startTime = Date.now()
  const startVal = props.startValue
  const endVal = props.endValue
  const duration = props.duration
  
  const step = () => {
    const now = Date.now()
    const elapsed = now - startTime
    const progress = Math.min(elapsed / duration, 1)
    
    let easedProgress = progress
    if (props.useEasing) {
      easedProgress = easeOutQuart(progress)
    }
    
    const currentValue = startVal + (endVal - startVal) * easedProgress
    displayValue.value = parseFloat(currentValue.toFixed(props.decimals))
    
    if (progress < 1) {
      requestAnimationFrame(step)
    } else {
      isAnimating.value = false
    }
  }
  
  requestAnimationFrame(step)
}

// 重置动画
const reset = () => {
  displayValue.value = props.startValue
  isAnimating.value = false
}

// 监听endValue变化，重新开始动画
watch(() => props.endValue, () => {
  reset()
  if (props.autoStart) {
    setTimeout(animate, 100)
  }
})

onMounted(() => {
  if (props.autoStart) {
    setTimeout(animate, 100)
  }
})

// 暴露方法给父组件
defineExpose({
  animate,
  reset
})
</script>

<style scoped>
.count-up {
  font-variant-numeric: tabular-nums;
  transition: all 0.3s ease;
  display: inline-block;
}

.count-up.counting {
  color: #4797C8;
  text-shadow: 0 0 10px rgba(71, 151, 200, 0.3);
  animation: glow 0.5s ease-in-out;
}

@keyframes glow {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 数字跳动效果 */
.count-up.counting::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(71, 151, 200, 0.1) 50%, transparent 70%);
  animation: shine 2s ease-in-out;
  pointer-events: none;
}

@keyframes shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
</style>
