<script setup lang="ts">
import AppLayout from '@/components/AppLayout.vue'
import DamModel3D from '@/components/DamModel3D.vue' // 导入三维模型组件
import { ref, computed } from 'vue'
import alertIcon from '@/assets/alert-icon.svg'

const selectedProject = ref('')

// 预警信息弹窗控制
const showAlertDialog = ref(false)
const alertInfo = ref({
  type: '测值异常',
  time: '2025-06-25 08:00',
  content: 'S2-2测值异常；S3-4测值异常',
  handleMethod: '系统通知',
  suggestion: '对S2-2，S3-4加密观测',
  notificationContent: 'S2-2测值异常；S3-4测值异常。请对S2-2，S3-4加密观测，检测是否为仪器数值波动。',
  receiver: '',
  approver: ''
})

// 显示报警信息
const showAlertInfo = () => {
  showAlertDialog.value = true
}

// 关闭弹窗
const closeAlertDialog = () => {
  showAlertDialog.value = false
}

// 处理预警
const handleAlert = () => {
  console.log('处理预警')
  closeAlertDialog()
}

// 评估结果列表数据
const evaluationList = ref([
  { id: 1, time: '2025-06-20', result: '安全' },
  { id: 2, time: '2025-06-19', result: '异常' },
  { id: 3, time: '2025-06-18', result: '安全' },
  { id: 4, time: '2025-06-17', result: '异常' },
  { id: 5, time: '2025-06-16', result: '安全' },
  { id: 6, time: '2025-06-15', result: '安全' },
  { id: 7, time: '2025-06-14', result: '异常' },
  { id: 8, time: '2025-06-13', result: '安全' },
  { id: 9, time: '2025-06-12', result: '安全' },
  { id: 10, time: '2025-06-11', result: '安全' },
  { id: 11, time: '2025-06-10', result: '异常' },
  { id: 12, time: '2025-06-09', result: '安全' },
  { id: 13, time: '2025-06-08', result: '安全' },
  { id: 14, time: '2025-06-07', result: '安全' },
  { id: 15, time: '2025-06-06', result: '异常' },
  { id: 16, time: '2025-06-05', result: '安全' },
  { id: 17, time: '2025-06-04', result: '安全' },
  { id: 18, time: '2025-06-03', result: '安全' },
  { id: 19, time: '2025-06-02', result: '异常' },
  { id: 20, time: '2025-06-01', result: '安全' }
])

// 分页相关变量
const currentPage = ref(1)
const pageSize = ref(20)
const pageSizeOptions = [10, 20, 50]

// 计算属性：当前页数据
const currentPageData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return evaluationList.value.slice(start, end)
})

// 计算属性：总页数
const totalPages = computed(() => {
  return Math.ceil(evaluationList.value.length / pageSize.value)
})

// 分页方法
const changePage = (page: number) => {
  if (page < 1 || page > totalPages.value) return
  currentPage.value = page
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

// 改变每页条数
const changePageSize = (event: Event) => {
  const select = event.target as HTMLSelectElement
  pageSize.value = parseInt(select.value)
  currentPage.value = 1 // 重置为第一页
}

// 查看评估详情
const viewEvaluationDetail = (id: number) => {
  console.log('查看评估详情', id)
  // 这里可以添加查看评估详情的逻辑
}
</script>

<template>
  <AppLayout>
    <div class="breadcrumb slide-in-left">
      <span class="path-prefix">大坝安全 / </span>
      <span class="path-current">综合评价</span>
    </div>

    <!-- 添加三维模型展示区域 -->
    <div class="content-container fade-in delay-200">
      <!-- 监测项目选择框和报警信息框 -->
      <div class="select-container">
        <select v-model="selectedProject" class="project-select">
          <option value="" disabled selected>请选择监测项目</option>
          <option value="project1">项目一</option>
          <option value="project2">项目二</option>
          <option value="project3">项目三</option>
        </select>
        <div class="alert-display" @click="showAlertInfo">
          <img :src="alertIcon" alt="报警" class="alert-icon" />
          <span class="alert-text">暂无报警信息</span>
        </div>
      </div>
      
      <!-- 使用与首页相同的三维模型组件 -->
      <DamModel3D class="dam-container-wrapper" />
      
      <div class="evaluation-container">
        <!-- 评估结果显示区域 -->
        <div class="evaluation-result-panel">
          <div class="evaluation-result-content">
            <div class="evaluation-result-label">当前评估结果:</div>
            <div class="evaluation-result-status danger">异常</div>
          </div>
          <div class="evaluation-trigger-btn">
            <span class="btn-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="22" y1="2" x2="11" y2="13"></line>
                <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
              </svg>
            </span>
            发起评估
          </div>
        </div>
        
        <div class="evaluation-update-time">
          最后更新时间: 2025-06-20
        </div>
        
        <!-- 评估结果列表表格 -->
        <div class="evaluation-list-section">
          <div class="section-title">评估结果列表</div>
          
          <div class="table-container">
            <div class="table-wrapper">
              <table>
                <thead>
                  <tr>
                    <th>序号</th>
                    <th>分析时间</th>
                    <th>结果</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="item in currentPageData" :key="item.id" class="table-row-hover">
                    <td>{{ item.id }}</td>
                    <td>{{ item.time }}</td>
                    <td>
                      <span :class="['result-tag', item.result === '安全' ? 'safe' : 'danger']">{{ item.result }}</span>
                    </td>
                    <td>
                      <a class="view-link" @click="viewEvaluationDetail(item.id)">查看</a>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            
            <div class="table-footer">
              <div class="total-records">共{{ evaluationList.length }}条记录</div>
              <div class="pagination">
                <div class="page-size-selector">
                  <span>{{ pageSize }}条/页</span>
                </div>
                <div class="pagination-controls">
                  <button class="page-nav" @click="prevPage" :disabled="currentPage <= 1">&lt;</button>
                  <button class="page-num active">{{ currentPage }}</button>
                  <button class="page-nav" @click="nextPage" :disabled="currentPage >= totalPages">&gt;</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 预警信息弹窗 -->
    <el-dialog
      v-model="showAlertDialog"
      title="预警信息"
      width="600px"
      :before-close="closeAlertDialog"
    >
      <div class="alert-info-section">
        <div class="alert-info-row">
          <span class="alert-info-label">预警类型：</span>
          <span class="alert-info-value">{{ alertInfo.type }}</span>
        </div>
        <div class="alert-info-row">
          <span class="alert-info-label">预警时间：</span>
          <span class="alert-info-value">{{ alertInfo.time }}</span>
        </div>
        <div class="alert-info-row">
          <span class="alert-info-label">预警内容：</span>
          <span class="alert-info-value">{{ alertInfo.content }}</span>
        </div>
      </div>

      <el-form label-width="100px" class="alert-form">
        <el-form-item label="处置方式" required>
          <el-select v-model="alertInfo.handleMethod" placeholder="请选择处置方式">
            <el-option label="系统通知" value="系统通知" />
            <el-option label="人工处理" value="人工处理" />
            <el-option label="忽略" value="忽略" />
          </el-select>
        </el-form-item>

        <el-form-item label="处置建议" required>
          <el-input
            v-model="alertInfo.suggestion"
            type="textarea"
            :rows="3"
            placeholder="对S2-2，S3-4加密观测"
          />
        </el-form-item>

        <el-form-item label="通知内容" required>
          <el-input
            v-model="alertInfo.notificationContent"
            type="textarea"
            :rows="3"
            placeholder="S2-2测值异常；S3-4测值异常。请对S2-2，S3-4加密观测，检测是否为仪器数值波动。"
          />
        </el-form-item>

        <el-form-item label="接收人" required>
          <el-select v-model="alertInfo.receiver" placeholder="请选择接收人">
            <el-option label="用户一" value="user1" />
            <el-option label="用户二" value="user2" />
          </el-select>
        </el-form-item>

        <el-form-item label="审批人" required>
          <el-select v-model="alertInfo.approver" placeholder="请选择审批人">
            <el-option label="用户一" value="user1" />
            <el-option label="用户二" value="user2" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeAlertDialog">取消</el-button>
          <el-button type="primary" @click="handleAlert">处理</el-button>
        </span>
      </template>
    </el-dialog>
  </AppLayout>
</template>

<style scoped>
.breadcrumb {
  width: 133px;
  height: 19px;
  font-family: Microsoft YaHei;
  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: normal;
  margin-left: 0px;
  margin-top: 0px;
  margin-bottom: 10px;
}

.path-prefix {
  color: #999999;
}

.path-current {
  color: #333333;
}

/* 添加三维模型展示区域的样式 */
.content-container {
  display: flex;
  position: relative;
  margin-top: 5px;
  box-shadow: 0px 0px 16px 0px rgba(18, 30, 46, 0.08);
  border-radius: 4px;
}

/* 监测项目选择框和报警信息框样式 */
.select-container {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
  display: flex;
  gap: 10px;
}

.project-select {
  width: 240px;
  height: 36px;
  border-radius: 2px;
  background: #FFFFFF;
  box-sizing: border-box;
  border: 1px solid #E1E4E9;
  padding: 0 10px;
  font-family: "Microsoft YaHei";
  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #BFBFBF;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' width='14' height='14' viewBox='0 0 24 24' fill='none' stroke='%23BFBFBF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 10px center;
}

.project-select option {
  color: #333333;
}

.project-select option:first-child {
  color: #BFBFBF;
}

.alert-display {
  width: 240px;
  height: 36px;
  border-radius: 2px;
  background: #FFFFFF;
  box-sizing: border-box;
  border: 1px solid #E1E4E9;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  cursor: pointer;
}

.alert-icon {
  width: 18px;
  height: 18px;
  margin-right: 10px;
}

.alert-text {
  font-family: "Microsoft YaHei";
  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #555555;
}

/* 与首页相同的三维模型容器样式 */
.dam-container-wrapper {
  width: calc(100% - 480px);
  height: 964px;
  flex-shrink: 0;
}

.evaluation-container {
  width: 480px;
  height: 964px;
  border-radius: 0px 4px 4px 0px;
  background: #FFFFFF;
  box-shadow: none;
  flex-shrink: 0;
  padding: 20px;
  box-sizing: border-box;
  border: 1px solid #e6e6e6;
  border-left: none;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 评估结果面板样式 */
.evaluation-result-panel {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  flex-shrink: 0;
}

.evaluation-result-content {
  display: flex;
  align-items: center;
  gap: 10px;
}

.evaluation-result-label {
  font-family: "Microsoft YaHei";
  font-size: 16px;
  font-weight: 600;
  color: #333333;
}

.evaluation-result-status {
  padding: 4px 16px;
  border-radius: 4px;
  font-family: "Microsoft YaHei";
  font-size: 14px;
  font-weight: 600;
  color: #FFFFFF;
}

.evaluation-result-status.safe {
  background-color: #4FCA89;
}

.evaluation-result-status.warning {
  background-color: #F6BD16;
}

.evaluation-result-status.danger {
  background-color: #F5222D;
}

.evaluation-update-time {
  font-family: "Microsoft YaHei";
  font-size: 14px;
  color: #999999;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #E1E4E9;
  flex-shrink: 0;
}

.evaluation-trigger-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 4px;
  background-color: #FFFFFF;
  border: 1px solid #4797C8;
  color: #4797C8;
  font-family: "Microsoft YaHei";
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.evaluation-trigger-btn:hover {
  background-color: rgba(71, 151, 200, 0.05);
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon svg {
  width: 14px;
  height: 14px;
  color: #4797C8;
}

/* 评估结果列表表格样式 */
.evaluation-list-section {
  margin-top: 0px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.section-title {
  font-family: "Microsoft YaHei";
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 15px;
  flex-shrink: 0;
}

.table-container {
  background-color: #F5F7FA;
  border-radius: 4px;
  padding: 10px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.table-wrapper {
  width: 100%;
  overflow: auto;
  border-radius: 4px;
  background-color: #FFFFFF;
  margin-bottom: 10px;
  flex-grow: 1;
}

table {
  width: 100%;
  border-collapse: collapse;
  white-space: nowrap;
}

thead {
  background-color: #F5F7FA;
  position: sticky;
  top: 0;
  z-index: 1;
}

th, td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #EBEEF5;
  font-family: "Microsoft YaHei";
  font-size: 14px;
  color: #606266;
}

th {
  font-weight: 600;
  color: #333333;
}

tbody tr:hover {
  background-color: #F5F7FA;
}

.result-tag {
  display: inline-block;
  padding: 2px 10px;
  border-radius: 2px;
  font-family: "Microsoft YaHei";
  font-size: 12px;
  color: #FFFFFF;
}

.result-tag.safe {
  background-color: #4FCA89;
}

.result-tag.warning {
  background-color: #F6BD16;
}

.result-tag.danger {
  background-color: #F5222D;
}

.view-link {
  color: #4797C8;
  cursor: pointer;
  text-decoration: none;
}

.view-link:hover {
  text-decoration: underline;
}

.table-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  flex-shrink: 0;
}

.total-records {
  font-family: "Microsoft YaHei";
  font-size: 14px;
  color: #606266;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 10px;
}

.page-size-selector {
  font-family: "Microsoft YaHei";
  font-size: 14px;
  color: #606266;
}

.pagination-controls {
  display: flex;
  align-items: center;
}

.page-nav, .page-num {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #DCDFE6;
  background-color: white;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 2px;
}

.page-nav:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.page-num.active {
  background-color: #4797C8;
  color: white;
  border-color: #4797C8;
}

/* 预警信息弹窗样式 */
.alert-info-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 4px solid #4797C8;
}

.alert-info-row {
  display: flex;
  margin-bottom: 10px;
  align-items: center;
}

.alert-info-row:last-child {
  margin-bottom: 0;
}

.alert-info-label {
  min-width: 80px;
  color: #606266;
  font-family: "Microsoft YaHei";
  font-size: 14px;
  font-weight: 600;
}

.alert-info-value {
  color: #333333;
  font-family: "Microsoft YaHei";
  font-size: 14px;
}

.alert-form {
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

@media (max-width: 1300px) {
  .content-container {
    flex-direction: column;
    height: auto;
  }
  .dam-container-wrapper, .evaluation-container {
    width: 100%;
    height: auto;
    border-left: 1px solid #e6e6e6;
  }
  .dam-container-wrapper {
     height: 600px; /* Maintain a reasonable height for the 3d model */
  }
}

@media (max-width: 768px) {
  .select-container {
    position: static;
    flex-direction: column;
    width: 100%;
    padding: 10px;
    gap: 10px;
    background: #f9f9f9;
    border-bottom: 1px solid #e6e6e6;
  }
  .project-select, .alert-display {
    width: 100%;
  }

  .dam-container-wrapper {
     height: 400px;
  }

  .evaluation-container {
    padding: 10px;
  }

  .table-footer {
    flex-direction: column;
    gap: 10px;
  }


}
</style> 