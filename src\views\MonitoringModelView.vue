<script setup lang="ts">
import AppLayout from '@/components/AppLayout.vue'
import { ref, computed, onMounted, onUnmounted } from 'vue'

interface MonitoringModel {
  id: number;
  name: string;
  type: string;
  accuracy: string;
  status: '运行中' | '已停止';
  lastUpdate: string;
}

// 屏幕宽度
const screenWidth = ref(window.innerWidth)

const handleResize = () => {
  screenWidth.value = window.innerWidth
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

const isMobile = computed(() => screenWidth.value < 768)

const modelDialogWidth = computed(() => isMobile.value ? '90%' : '70%')
const trainingDialogWidth = computed(() => isMobile.value ? '90%' : '60%')


// 监控模型数据
const monitoringModels = ref<MonitoringModel[]>([
  { id: 1, name: '水位监测模型', type: '回归分析', accuracy: '92%', status: '运行中', lastUpdate: '2023-10-14' },
  { id: 2, name: '变形预测模型', type: '神经网络', accuracy: '89%', status: '运行中', lastUpdate: '2023-10-13' },
  { id: 3, name: '渗流分析模型', type: '有限元', accuracy: '94%', status: '运行中', lastUpdate: '2023-10-12' },
  { id: 4, name: '结构稳定性模型', type: '力学分析', accuracy: '91%', status: '已停止', lastUpdate: '2023-09-28' },
  { id: 5, name: '水文预测模型', type: '时间序列', accuracy: '87%', status: '运行中', lastUpdate: '2023-10-10' }
])

// 当前选中的模型
const currentModel = ref(monitoringModels.value[0])

// 模型参数
const modelParams = ref({
  learningRate: 0.01,
  epochs: 1000,
  batchSize: 32,
  inputFeatures: ['水位', '降雨量', '温度', '上游流量'],
  outputFeatures: ['未来24小时水位预测']
})

// 模型性能指标
const performanceMetrics = ref({
  accuracy: 92.5,
  precision: 90.8,
  recall: 94.2,
  f1Score: 92.4,
  rmse: 0.35
})

// 辅助数据
const searchQuery = ref('')
const selectedModel = ref(1)
const datasetOption = ref('recent')
const trainTestSplit = ref(80)

// 对话框控制
const showModelDialog = ref(false)
const showTrainingDialog = ref(false)

// 打开模型详情对话框
const openModelDialog = (model: MonitoringModel) => {
  currentModel.value = model
  showModelDialog.value = true
}

// 打开训练对话框
const openTrainingDialog = () => {
  showTrainingDialog.value = true
}

// 关闭对话框
const handleClose = () => {
  showModelDialog.value = false
  showTrainingDialog.value = false
}

// 启动/停止模型
const toggleModelStatus = (model: MonitoringModel) => {
  model.status = model.status === '运行中' ? '已停止' : '运行中'
}

// 开始训练模型
const startTraining = () => {
  handleClose()
  // 实际应用中这里应该有训练逻辑
}
</script>

<template>
  <AppLayout>
    <div class="model-container">
      <div class="page-header">
        <h1>监控模型</h1>
        <div class="header-actions">
          <el-button type="success">创建模型</el-button>
          <el-button type="primary" @click="openTrainingDialog">训练模型</el-button>
        </div>
      </div>
      
      <div class="models-section">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>监控模型列表</span>
              <el-input
                v-model="searchQuery"
                placeholder="搜索模型"
                style="width: 200px"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>
          </template>
          <div class="models-table">
            <el-table :data="monitoringModels" style="width: 100%">
              <el-table-column prop="name" label="模型名称" width="180" />
              <el-table-column prop="type" label="模型类型" width="150" />
              <el-table-column prop="accuracy" label="准确率" width="100" />
              <el-table-column prop="status" label="状态" width="120">
                <template #default="scope">
                  <el-tag :type="scope.row.status === '运行中' ? 'success' : 'info'">
                    {{ scope.row.status }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="lastUpdate" label="最后更新" width="150" />
              <el-table-column label="操作">
                <template #default="scope">
                  <el-button type="text" @click="openModelDialog(scope.row)">查看详情</el-button>
                  <el-button type="text" @click="toggleModelStatus(scope.row)">
                    {{ scope.row.status === '运行中' ? '停止' : '启动' }}
                  </el-button>
                  <el-button type="text">编辑</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </div>
      
      <div class="model-details-section" v-if="currentModel">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>当前模型: {{ currentModel.name }}</span>
              <el-tag :type="currentModel.status === '运行中' ? 'success' : 'info'">
                {{ currentModel.status }}
              </el-tag>
            </div>
          </template>
          <div class="model-overview">
            <div class="model-info">
              <h3>模型信息</h3>
              <p><strong>模型类型:</strong> {{ currentModel.type }}</p>
              <p><strong>准确率:</strong> {{ currentModel.accuracy }}</p>
              <p><strong>最后更新:</strong> {{ currentModel.lastUpdate }}</p>
              <p><strong>描述:</strong> 该模型用于监测和预测大坝相关参数，基于历史数据进行分析和预警。</p>
            </div>
            
            <div class="model-performance">
              <h3>性能指标</h3>
              <div class="metrics-grid">
                <div class="metric-item">
                  <div class="metric-label">准确率</div>
                  <div class="metric-value">{{ performanceMetrics.accuracy }}%</div>
                </div>
                <div class="metric-item">
                  <div class="metric-label">精确率</div>
                  <div class="metric-value">{{ performanceMetrics.precision }}%</div>
                </div>
                <div class="metric-item">
                  <div class="metric-label">召回率</div>
                  <div class="metric-value">{{ performanceMetrics.recall }}%</div>
                </div>
                <div class="metric-item">
                  <div class="metric-label">F1分数</div>
                  <div class="metric-value">{{ performanceMetrics.f1Score }}</div>
                </div>
                <div class="metric-item">
                  <div class="metric-label">RMSE</div>
                  <div class="metric-value">{{ performanceMetrics.rmse }}</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="model-visualization">
            <h3>模型可视化</h3>
            <div class="chart-placeholder">
              图表区域 - 模型预测结果与实际数据对比
            </div>
          </div>
        </el-card>
      </div>
    </div>
    
    <!-- 模型详情对话框 -->
    <el-dialog
      v-model="showModelDialog"
      title="模型详情"
      :width="modelDialogWidth"
    >
      <div class="model-detail-content" v-if="currentModel">
        <h2>{{ currentModel.name }}</h2>
        
        <div class="detail-section">
          <h3>基本信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="模型名称">{{ currentModel.name }}</el-descriptions-item>
            <el-descriptions-item label="模型类型">{{ currentModel.type }}</el-descriptions-item>
            <el-descriptions-item label="准确率">{{ currentModel.accuracy }}</el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="currentModel.status === '运行中' ? 'success' : 'info'">
                {{ currentModel.status }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="最后更新">{{ currentModel.lastUpdate }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">2023-08-15</el-descriptions-item>
          </el-descriptions>
        </div>
        
        <div class="detail-section">
          <h3>模型参数</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="学习率">{{ modelParams.learningRate }}</el-descriptions-item>
            <el-descriptions-item label="训练轮次">{{ modelParams.epochs }}</el-descriptions-item>
            <el-descriptions-item label="批次大小">{{ modelParams.batchSize }}</el-descriptions-item>
            <el-descriptions-item label="输入特征">{{ modelParams.inputFeatures.join(', ') }}</el-descriptions-item>
            <el-descriptions-item label="输出特征">{{ modelParams.outputFeatures.join(', ') }}</el-descriptions-item>
          </el-descriptions>
        </div>
        
        <div class="detail-section">
          <h3>性能评估</h3>
          <div class="performance-charts">
            <div class="chart-placeholder half-width">
              图表区域 - 训练历史
            </div>
            <div class="chart-placeholder half-width">
              图表区域 - 混淆矩阵
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">关闭</el-button>
          <el-button type="primary" @click="openTrainingDialog">重新训练</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 训练模型对话框 -->
    <el-dialog
      v-model="showTrainingDialog"
      title="模型训练"
      :width="trainingDialogWidth"
    >
      <div class="training-form">
        <el-form label-width="120px">
          <el-form-item label="选择模型">
            <el-select v-model="selectedModel" placeholder="请选择模型">
              <el-option
                v-for="model in monitoringModels"
                :key="model.id"
                :label="model.name"
                :value="model.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="学习率">
            <el-slider v-model="modelParams.learningRate" :min="0.001" :max="0.1" :step="0.001" show-input :format-tooltip="(val: number) => val.toFixed(3)" />
          </el-form-item>
          <el-form-item label="训练轮次">
            <el-slider v-model="modelParams.epochs" :min="100" :max="5000" :step="100" show-input />
          </el-form-item>
          <el-form-item label="批次大小">
            <el-slider v-model="modelParams.batchSize" :min="8" :max="128" :step="8" show-input />
          </el-form-item>
          <el-form-item label="数据集选择">
            <el-radio-group v-model="datasetOption">
              <el-radio label="all">全部数据</el-radio>
              <el-radio label="recent">最近一年数据</el-radio>
              <el-radio label="custom">自定义</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="训练/测试比例">
            <el-slider v-model="trainTestSplit" :min="50" :max="90" :step="5" show-input :format-tooltip="(val: number) => `${val}% / ${100-val}%`" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="startTraining">开始训练</el-button>
        </span>
      </template>
    </el-dialog>
  </AppLayout>
</template>

<script>
// 辅助数据
const searchQuery = ref('')
const selectedModel = ref(1)
const datasetOption = ref('recent')
const trainTestSplit = ref(80)
</script>

<style scoped>
.model-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

h1 {
  margin: 0;
  color: #303133;
}

.models-section {
  margin-bottom: 30px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.models-table {
  padding: 10px 0;
}

.model-details-section {
  margin-bottom: 30px;
}

.model-overview {
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
}

.model-info, .model-performance {
  flex: 1;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-top: 20px;
}

.metric-item {
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  text-align: center;
}

.metric-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 22px;
  font-weight: bold;
  color: #409EFF;
}

.model-visualization {
  margin-bottom: 20px;
}

.chart-placeholder {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  color: #909399;
  border-radius: 4px;
}

.detail-section {
  margin-bottom: 30px;
}

h2, h3 {
  border-bottom: 1px solid #EBEEF5;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

.performance-charts {
  display: flex;
  gap: 20px;
}

.half-width {
  width: 50%;
}

.training-form {
  padding: 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }
  .model-overview {
    flex-direction: column;
  }
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  .performance-charts {
    flex-direction: column;
  }
  .half-width {
    width: 100%;
  }
}

@media (max-width: 480px) {
    .metrics-grid {
        grid-template-columns: 1fr;
    }
}
</style> 