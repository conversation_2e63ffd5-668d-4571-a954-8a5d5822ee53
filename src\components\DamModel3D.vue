<script setup lang="ts">
import { ref, onMounted, onUnmounted, reactive } from 'vue' // NEW: imported reactive
import * as echarts from 'echarts'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'

const threeContainer = ref(null)

// 预设固定标记点数据
const fixedMarkersData = [
  { name: '坝0+500', position: { x: 0.040, y: 0.100, z: -0.519 } },
  { name: '坝0+400', position: { x: 0.041, y: 0.100, z: -0.362 } },
  { name: '坝0+271', position: { x: 0.040, y: 0.100, z: 0.159 } },
  { name: '坝0+205', position: { x: 0.040, y: 0.100, z: 0.330 } },
  { name: '坝0+150', position: { x: 0.040, y: 0.100, z: 0.506 } },
  { name: '坝0+101', position: { x: 0.042, y: 0.100, z: 0.680 } }
]

// three.js 相关变量
let scene, camera, renderer, controls
let animationFrameId
const animationFunctions = []
let damModel
let markers = ref([])
const mouse = new THREE.Vector2()
const raycasterObj = new THREE.Raycaster()

// 弹窗相关
const popupVisible = ref(false)
const popupMarker = ref(null)

// 剖面图相关
const sectionCanvas = ref(null)
const sectionPoints = [
  { x: 0, y: 25, label: 'S1-4', value: '1.8' },
  { x: 40, y: 10, label: 'S2-4', value: '25.4' },
  { x: 70, y: 15, label: 'S3-4', value: '11.4' },
  { x: 95, y: 23, label: 'S4-4', value: '1.4' }
]

// 测点趋势图弹窗相关
const trendPopupVisible = ref(false)
const selectedPoint = ref(null)
const trendChartRef = ref(null)
const trendChartInstance = ref(null)

// 测点历史数据（模拟数据）
const pointHistoryData = {
  'S1-4': {
    maxValue: 20.2,
    recordDate: '2025-06-15',
    trend: [
      { date: '2015.01.01', value: 0.5 },
      { date: '2016.01.01', value: 9.2 },
      { date: '2017.01.01', value: -2.1 },
      { date: '2018.01.01', value: 6.7 },
      { date: '2019.01.01', value: 1.8 },
      { date: '2020.01.01', value: 6.5 },
      { date: '2021.01.01', value: -3.8 },
      { date: '2022.01.01', value: 1.2 },
      { date: '2023.01.01', value: -1.5 },
      { date: '2024.01.01', value: 5.3 },
      { date: '2025.06.18', value: 4.1 }
    ]
  },
  'S2-4': {
    maxValue: 25.4,
    recordDate: '2025-06-10',
    trend: [
      { date: '2015.01.01', value: 2.1 },
      { date: '2016.01.01', value: 12.5 },
      { date: '2017.01.01', value: 4.3 },
      { date: '2018.01.01', value: 8.9 },
      { date: '2019.01.01', value: 5.7 },
      { date: '2020.01.01', value: 9.2 },
      { date: '2021.01.01', value: 4.8 },
      { date: '2022.01.01', value: 1.5 },
      { date: '2023.01.01', value: 3.9 },
      { date: '2024.01.01', value: -1.2 },
      { date: '2025.06.18', value: 7.3 }
    ]
  },
  'S3-4': {
    maxValue: 18.7,
    recordDate: '2025-05-20',
    trend: [
      { date: '2015.01.01', value: 1.3 },
      { date: '2016.01.01', value: 7.8 },
      { date: '2017.01.01', value: 3.2 },
      { date: '2018.01.01', value: 5.4 },
      { date: '2019.01.01', value: 6.7 },
      { date: '2020.01.01', value: 2.9 },
      { date: '2021.01.01', value: 8.5 },
      { date: '2022.01.01', value: 0.8 },
      { date: '2023.01.01', value: 2.6 },
      { date: '2024.01.01', value: 4.5 },
      { date: '2025.06.18', value: 3.2 }
    ]
  },
  'S4-4': {
    maxValue: 15.6,
    recordDate: '2025-04-18',
    trend: [
      { date: '2015.01.01', value: 0.9 },
      { date: '2016.01.01', value: 4.2 },
      { date: '2017.01.01', value: 1.5 },
      { date: '2018.01.01', value: 3.1 },
      { date: '2019.01.01', value: 5.8 },
      { date: '2020.01.01', value: 2.7 },
      { date: '2021.01.01', value: 4.9 },
      { date: '2022.01.01', value: -2.3 },
      { date: '2023.01.01', value: 0.5 },
      { date: '2024.01.01', value: -1.1 },
      { date: '2025.06.18', value: 1.4 }
    ]
  }
}

// NEW: 坐标拾取相关状态
const isCoordDisplayEnabled = ref(true)
const isCoordVisible = ref(true) // 改为默认显示
const mouseCoords3D = ref({ x: 0.000, y: 0.000, z: 0.000 })
const tooltipPosition = reactive({ x: 0, y: 0 })

// NEW: 相机信息显示控制
const isCameraInfoVisible = ref(false) // 修改为默认隐藏

// NEW: 处理鼠标移动以拾取坐标 - 优化版本
let mouseMoveTimeout: number | null = null
const handleMouseMove = (event: MouseEvent) => {
  if (!isCoordDisplayEnabled.value || !renderer || !camera || !damModel) {
    return
  }

  // 防抖处理，减少计算频率
  if (mouseMoveTimeout) {
    clearTimeout(mouseMoveTimeout)
  }

  mouseMoveTimeout = setTimeout(() => {
    const rect = renderer.domElement.getBoundingClientRect()
    mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
    mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

    raycasterObj.setFromCamera(mouse, camera)
    const intersects = raycasterObj.intersectObject(damModel, true)

    if (intersects.length > 0) {
      const point = intersects[0].point
      const newCoords = {
        x: parseFloat(point.x.toFixed(3)),
        y: parseFloat(point.y.toFixed(3)),
        z: parseFloat(point.z.toFixed(3))
      }

      // 平滑更新坐标值
      if (!mouseCoords3D.value) {
        mouseCoords3D.value = newCoords
      } else {
        const lerpFactor = 0.3
        mouseCoords3D.value = {
          x: parseFloat((mouseCoords3D.value.x + (newCoords.x - mouseCoords3D.value.x) * lerpFactor).toFixed(3)),
          y: parseFloat((mouseCoords3D.value.y + (newCoords.y - mouseCoords3D.value.y) * lerpFactor).toFixed(3)),
          z: parseFloat((mouseCoords3D.value.z + (newCoords.z - mouseCoords3D.value.z) * lerpFactor).toFixed(3))
        }
      }

      // 优化: 调整提示框位置，更靠近鼠标，添加平滑移动
      const margin = 10
      const targetX = Math.min(event.clientX + margin, window.innerWidth - 200)
      const targetY = Math.min(event.clientY + margin, window.innerHeight - 120)

      // 平滑移动提示框
      const smoothFactor = 0.2
      tooltipPosition.x = tooltipPosition.x + (targetX - tooltipPosition.x) * smoothFactor
      tooltipPosition.y = tooltipPosition.y + (targetY - tooltipPosition.y) * smoothFactor

      isCoordVisible.value = true
    } else {
      isCoordVisible.value = false
    }
  }, 16) // 约60fps的更新频率
}

// NEW: 切换坐标显示功能
const toggleCoordDisplay = () => {
  isCoordDisplayEnabled.value = !isCoordDisplayEnabled.value
}

// NEW: 切换相机信息显示
const toggleCameraInfo = () => {
  isCameraInfoVisible.value = !isCameraInfoVisible.value
}


// 处理模型点击
const handleModelClick = (event) => {
  if (!scene || !camera) return
  
  const rect = renderer.domElement.getBoundingClientRect()
  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1
  
  raycasterObj.setFromCamera(mouse, camera)
  
  const markerMeshes = []
  markers.value.forEach(marker => {
    if (marker.mesh) {
      markerMeshes.push(marker.mesh)
      if (marker.arrowGroup) {
        marker.arrowGroup.children.forEach(child => {
          child.userData.markerId = marker.id
          markerMeshes.push(child)
        })
      }
    }
  })
  
  const markerIntersects = raycasterObj.intersectObjects(markerMeshes, true)
  
  if (markerIntersects.length > 0) {
    const clickedObject = markerIntersects[0].object
    let markerId = null
    
    if (clickedObject.userData.markerId) {
      markerId = clickedObject.userData.markerId
    } 
    else if (clickedObject.parent && clickedObject.parent.userData.markerId) {
      markerId = clickedObject.parent.userData.markerId
    }
    else {
      let currentObject = clickedObject
      while(currentObject) {
        if (currentObject.userData.id && markers.value.some(m => m.id === currentObject.userData.id)) {
          markerId = currentObject.userData.id
          break
        }
        currentObject = currentObject.parent
      }
    }
    
    const clickedMarker = markers.value.find(m => m.id === markerId)
    
    if (clickedMarker) {
      showMarkerPopup(clickedMarker, event)
    }
  }
}

// 添加标记点
const addMarker = (position, name = null) => {
  const markerGroup = new THREE.Group()
  markerGroup.position.copy(position)
  
  const arrowGroup = createTechArrow(name)
  arrowGroup.position.y = 0.08
  markerGroup.add(arrowGroup)
  
  const markerId = Date.now().toString()
  markerGroup.userData.id = markerId
  
  if (name) {
    markerGroup.userData.name = name
  }
  
  const animateMarker = () => {
    if (!markerGroup.parent) return
    const time = Date.now() * 0.001

    // 更流畅的浮动动画
    const floatOffset = Math.sin(time * 2) * 0.015
    const pulseScale = 1 + Math.sin(time * 4) * 0.1

    const cone = arrowGroup.children.find(child => child.geometry && child.geometry.type === 'ConeGeometry')
    if (cone) {
      cone.position.y = 0.01 + floatOffset
      cone.scale.setScalar(pulseScale)

      // 添加旋转动画
      cone.rotation.y += 0.01
    }

    // 为标签添加轻微的摇摆动画
    const label = arrowGroup.children.find(child => child.geometry && child.geometry.type === 'PlaneGeometry')
    if (label) {
      label.rotation.z = Math.sin(time * 1.5) * 0.05
    }
  }
  
  animationFunctions.push(animateMarker)
  scene.add(markerGroup)
  
  const newMarker = {
    id: markerId,
    name: name,
    position: {
      x: position.x.toFixed(3),
      y: position.y.toFixed(3),
      z: position.z.toFixed(3)
    },
    mesh: markerGroup,
    arrowGroup: arrowGroup
  }
  
  markers.value.push(newMarker)
  console.log(`添加标记点 #${markers.value.length} (${name || '无名称'}):`, newMarker.position)
  return newMarker
}

// 创建标记样式
const createTechArrow = (markerName = null) => {
  const arrowGroup = new THREE.Group()
  
  const coneGeometry = new THREE.ConeGeometry(0.035, 0.07, 32)
  coneGeometry.rotateX(Math.PI)
  
  const coneVertexShader = `
    varying vec2 vUv;
    void main() {
      vUv = uv;
      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
  `
  
  const coneFragmentShader = `
    varying vec2 vUv;
    void main() {
      float opacity = 0.1 + vUv.y * 0.8;
      vec3 color = vec3(0.28, 0.59, 0.78);
      gl_FragColor = vec4(color, opacity);
    }
  `
  
  const coneMaterial = new THREE.ShaderMaterial({
    uniforms: {},
    vertexShader: coneVertexShader,
    fragmentShader: coneFragmentShader,
    transparent: true,
    side: THREE.DoubleSide
  })
  
  const cone = new THREE.Mesh(coneGeometry, coneMaterial)
  cone.position.y = 0.01
  arrowGroup.add(cone)
  
  if (markerName) {
    const canvas = document.createElement('canvas')
    canvas.width = 128
    canvas.height = 64
    const context = canvas.getContext('2d')
    if (context) {
      context.fillStyle = 'rgba(0, 0, 0, 0.4)';
      context.fillRect(0, 0, canvas.width, canvas.height);
      
      let damText = '';
      let numberText = '';
      
      if (markerName && markerName.includes('+')) {
        const parts = markerName.split('+');
        damText = parts[0] + '+';
        numberText = parts[1];
      } else {
        damText = markerName || '';
      }
      
      context.shadowColor = 'rgba(0, 0, 0, 0.7)';
      context.shadowBlur = 4;
      context.shadowOffsetX = 1;
      context.shadowOffsetY = 1;
      
      context.font = '600 24px "Microsoft YaHei"';
      context.textAlign = 'right';
      context.textBaseline = 'middle';
      context.fillStyle = '#FFFFFF';
      context.fillText(damText, canvas.width / 2 - 2, canvas.height / 2);
      
      if (numberText) {
        context.font = '700 28px "DIN Alternate", "Arial", sans-serif';
        context.textAlign = 'left';
        context.fillText(numberText, canvas.width / 2 - 2, canvas.height / 2);
      }
      
      const texture = new THREE.CanvasTexture(canvas)
      texture.needsUpdate = true
      
      const labelMaterial = new THREE.MeshBasicMaterial({
        map: texture,
        transparent: true,
        side: THREE.DoubleSide
      })
      
      const labelGeometry = new THREE.PlaneGeometry(0.25, 0.08)
      const label = new THREE.Mesh(labelGeometry, labelMaterial)
      label.position.y = 0.12
      arrowGroup.add(label)
    }
  }
  
  return arrowGroup
}

// 显示标记点弹窗
const showMarkerPopup = (marker, event) => {
  if (!marker) return
  popupMarker.value = marker
  popupVisible.value = true
  setTimeout(() => {
    drawSectionView()
  }, 50)
}

// 处理断面选择变化
const onSectionChange = (event) => {
  const selectedName = event.target.value
  const selectedMarker = markers.value.find(m => m.name === selectedName)
  if (selectedMarker) {
    popupMarker.value = selectedMarker
    setTimeout(() => {
      drawSectionView()
    }, 50)
  }
}

// 关闭弹窗
const closePopup = () => {
  popupVisible.value = false
}

// 绘制剖面图
const drawSectionView = () => {
  if (!sectionCanvas.value) return
  const canvas = sectionCanvas.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return
  
  canvas.width = canvas.clientWidth
  canvas.height = canvas.clientHeight
  ctx.clearRect(0, 0, canvas.width, canvas.height)
  
  const margin = 50
  const scaleX = (canvas.width - margin * 2) / 100
  const scaleY = (canvas.height - margin * 2) / 40
  
  ctx.fillStyle = '#FFFFFF'
  ctx.fillRect(0, 0, canvas.width, canvas.height)
  ctx.translate(margin, margin)
  
  ctx.beginPath()
  ctx.moveTo(5 * scaleX, 30 * scaleY)
  ctx.lineTo(15 * scaleX, 30 * scaleY)
  ctx.lineTo(20 * scaleX, 25 * scaleY)
  ctx.lineTo(40 * scaleX, 10 * scaleY)
  ctx.lineTo(70 * scaleX, 15 * scaleY)
  ctx.lineTo(95 * scaleX, 25 * scaleY)
  ctx.lineTo(95 * scaleX, 30 * scaleY)
  ctx.lineTo(5 * scaleX, 30 * scaleY)
  ctx.strokeStyle = '#666'
  ctx.lineWidth = 2
  ctx.stroke()
  
  ctx.beginPath()
  ctx.moveTo(5 * scaleX, 20 * scaleY)
  ctx.lineTo(95 * scaleX, 20 * scaleY)
  ctx.strokeStyle = '#4797C8'
  ctx.lineWidth = 1
  ctx.setLineDash([5, 3])
  ctx.stroke()
  ctx.setLineDash([])
  
  ctx.beginPath()
  ctx.moveTo(50 * scaleX, 5 * scaleY)
  ctx.lineTo(50 * scaleX, 35 * scaleY)
  ctx.strokeStyle = '#999'
  ctx.lineWidth = 1
  ctx.setLineDash([5, 3])
  ctx.stroke()
  ctx.setLineDash([])
  
  const pointHitAreas = []
  
  sectionPoints.forEach(point => {
    ctx.beginPath()
    ctx.arc(point.x * scaleX, point.y * scaleY, 6, 0, Math.PI * 2)
    ctx.fillStyle = '#4797C8'
    ctx.fill()
    
    pointHitAreas.push({
      label: point.label,
      x: point.x * scaleX,
      y: point.y * scaleY,
      radius: 6
    })
    
    const labelWidth = 50
    const labelHeight = 40
    const labelX = point.x * scaleX - labelWidth / 2
    const labelY = point.y * scaleY - labelHeight - 10
    
    ctx.fillStyle = '#FFFFFF'
    ctx.fillRect(labelX, labelY, labelWidth, labelHeight)
    ctx.strokeStyle = '#E1E4E9'
    ctx.lineWidth = 1
    ctx.strokeRect(labelX, labelY, labelWidth, labelHeight)
    
    pointHitAreas.push({
      label: point.label,
      x: labelX + labelWidth / 2,
      y: labelY + labelHeight / 2,
      width: labelWidth,
      height: labelHeight
    })
    
    ctx.fillStyle = '#333'
    ctx.font = '12px Microsoft YaHei'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText(point.label, labelX + labelWidth / 2, labelY + 15)
    
    ctx.fillStyle = '#333'
    ctx.font = 'bold 14px Microsoft YaHei'
    ctx.fillText(point.value, labelX + labelWidth / 2, labelY + 30)
    
    ctx.beginPath()
    ctx.moveTo(point.x * scaleX, point.y * scaleY - 6)
    ctx.lineTo(point.x * scaleX, labelY + labelHeight)
    ctx.strokeStyle = '#4797C8'
    ctx.lineWidth = 1
    ctx.stroke()
  })
  
  ctx.setTransform(1, 0, 0, 1, 0, 0)
  ctx.fillStyle = '#333'
  ctx.font = 'bold 20px Microsoft YaHei'
  ctx.textAlign = 'center'
  ctx.textBaseline = 'top'
  if (popupMarker.value) {
    ctx.fillText(`${popupMarker.value.name} 剖面图`, canvas.width / 2, 20)
  } else {
    ctx.fillText('大坝剖面图', canvas.width / 2, 20)
  }
  
  canvas.onclick = (event) => {
    const rect = canvas.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top
    
    for (const area of pointHitAreas) {
      let hit = false
      if ('radius' in area) {
        const distance = Math.sqrt(Math.pow(x - area.x, 2) + Math.pow(y - area.y, 2))
        hit = distance <= area.radius
      } else if ('width' in area && 'height' in area) {
        hit = x >= area.x - area.width / 2 && 
              x <= area.x + area.width / 2 && 
              y >= area.y - area.height / 2 && 
              y <= area.y + area.height / 2
      }
      if (hit) {
        showPointTrendPopup(area.label)
        break
      }
    }
  }
}

// 显示测点趋势图弹窗
const showPointTrendPopup = (pointLabel) => {
  if (!pointHistoryData[pointLabel]) return
  selectedPoint.value = {
    label: pointLabel,
    data: pointHistoryData[pointLabel]
  }
  trendPopupVisible.value = true
  setTimeout(() => {
    initTrendChart()
  }, 50)
}

// 关闭趋势图弹窗
const closeTrendPopup = () => {
  trendPopupVisible.value = false
  if (trendChartInstance.value) {
    trendChartInstance.value.dispose()
    trendChartInstance.value = null
  }
}

// 初始化趋势图
const initTrendChart = () => {
  if (!trendChartRef.value || !selectedPoint.value) return
  if (trendChartInstance.value) {
    trendChartInstance.value.dispose()
  }
  trendChartInstance.value = echarts.init(trendChartRef.value)
  const pointData = selectedPoint.value.data
  const dates = pointData.trend.map(item => item.date)
  const values = pointData.trend.map(item => item.value)
  
  const option = {
    title: { text: `${selectedPoint.value.label} 测值趋势`, left: 'center', top: 10 },
    tooltip: { trigger: 'axis', formatter: '{b}: {c} mm' },
    grid: { left: '5%', right: '5%', bottom: '10%', top: '15%', containLabel: true },
    xAxis: { type: 'category', data: dates, axisLabel: { interval: 0, rotate: 45 } },
    yAxis: { type: 'value', name: '测值/mm', axisLine: { show: true }, splitLine: { lineStyle: { type: 'dashed' } } },
    series: [{
      name: '测值', type: 'line', data: values, smooth: true, symbol: 'circle', symbolSize: 8,
      itemStyle: { color: '#4797C8' }, lineStyle: { width: 3, color: '#4797C8' },
      areaStyle: {
        color: {
          type: 'linear', x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [{ offset: 0, color: 'rgba(71, 151, 200, 0.3)' }, { offset: 1, color: 'rgba(71, 151, 200, 0)' }]
        }
      }
    }]
  }
  trendChartInstance.value.setOption(option)
  window.addEventListener('resize', () => {
    if (trendChartInstance.value && trendPopupVisible.value) {
      trendChartInstance.value.resize()
    }
  })
}

// 目标相机位置和旋转角度
const targetCameraSettings = {
  target: new THREE.Vector3(-0.276, 0.077, -0.010),
  rotation: { x: -13.8 * Math.PI / 180, y: -90.7 * Math.PI / 180, z: 0.0 * Math.PI / 180 },
  distance: 1.37
}

// 初始化3D场景
const init3DScene = () => {
  if (!threeContainer.value) return
  
  scene = new THREE.Scene()
  scene.background = new THREE.Color(0xf7f7f7)
  
  camera = new THREE.PerspectiveCamera(45, threeContainer.value.clientWidth / threeContainer.value.clientHeight, 0.1, 1000)
  camera.position.set(3, 3, 3)
  camera.lookAt(0, 0, 0)
  
  // 设置初始坐标值为目标点
  mouseCoords3D.value = {
    x: parseFloat(targetCameraSettings.target.x.toFixed(3)),
    y: parseFloat(targetCameraSettings.target.y.toFixed(3)),
    z: parseFloat(targetCameraSettings.target.z.toFixed(3))
  }
  
  const loadingManager = new THREE.LoadingManager()
  loadingManager.onLoad = () => {
    console.log('Loading complete!')
    animateCameraToTarget()
  }
  
  const loader = new GLTFLoader(loadingManager)
  loader.load('/models/ImageToStl.com_DAM.glb', (gltf) => {
    damModel = gltf.scene
    const box = new THREE.Box3().setFromObject(damModel)
    const size = box.getSize(new THREE.Vector3())
    const maxDim = Math.max(size.x, size.y, size.z)
    const scale = 2.0 / maxDim
    damModel.scale.set(scale, scale, scale)
    const center = box.getCenter(new THREE.Vector3())
    damModel.position.x = -center.x * scale
    damModel.position.y = -center.y * scale
    damModel.position.z = -center.z * scale

    // 初始设置模型为不可见，用于淡入动画
    damModel.traverse((node) => {
      if (node.isMesh) {
        node.material.metalness = 0.3
        node.material.roughness = 0.7
        node.material.envMapIntensity = 1.5
        node.material.side = THREE.DoubleSide
        node.material.transparent = true
        node.material.opacity = 0
      }
    })

    scene.add(damModel)

    // 模型淡入动画
    const fadeInDuration = 1500
    const startTime = Date.now()

    const fadeInAnimation = () => {
      const elapsedTime = Date.now() - startTime
      const progress = Math.min(elapsedTime / fadeInDuration, 1)
      const opacity = progress

      damModel.traverse((node) => {
        if (node.isMesh) {
          node.material.opacity = opacity
        }
      })

      if (progress < 1) {
        requestAnimationFrame(fadeInAnimation)
      } else {
        // 动画完成后，移除透明度设置以提高性能
        damModel.traverse((node) => {
          if (node.isMesh) {
            node.material.transparent = false
            node.material.opacity = 1
          }
        })
        isLoading.value = false
      }
    }

    fadeInAnimation()
  })
  
  renderer = new THREE.WebGLRenderer({ antialias: true })
  renderer.setSize(threeContainer.value.clientWidth, threeContainer.value.clientHeight)
  renderer.domElement.style.width = '100%'
  renderer.domElement.style.height = '100%'
  threeContainer.value.appendChild(renderer.domElement)
  renderer.domElement.addEventListener('click', handleModelClick)
  renderer.domElement.addEventListener('mousemove', handleMouseMove)
  renderer.domElement.addEventListener('mouseleave', () => { isCoordVisible.value = false })
  
  controls = new OrbitControls(camera, renderer.domElement)
  controls.enableDamping = true
  controls.dampingFactor = 0.1
  controls.rotateSpeed = 0.8
  controls.enableZoom = true
  controls.zoomSpeed = 1.2
  controls.enablePan = true
  controls.panSpeed = 0.8
  controls.minDistance = 1
  controls.maxDistance = 10
  controls.minPolarAngle = 0
  controls.maxPolarAngle = Math.PI
  controls.autoRotate = false
  
  addControlsGizmo()
  
  scene.add(new THREE.AmbientLight(0xffffff, 0.6))
  const mainLight = new THREE.DirectionalLight(0xffffff, 0.8)
  mainLight.position.set(5, 10, 7)
  scene.add(mainLight)
  const fillLight = new THREE.DirectionalLight(0xffffff, 0.3)
  fillLight.position.set(-5, 2, -7)
  scene.add(fillLight)
  const hemiLight = new THREE.HemisphereLight(0xffffff, 0x444444, 0.3)
  hemiLight.position.set(0, 20, 0)
  scene.add(hemiLight)
  
  window.addEventListener('resize', onWindowResize)
  animate()
}

// 相机动画 - 使用更高级的缓动函数
const animateCameraToTarget = () => {
  if (!camera || !controls) return
  console.log('开始相机动画...')
  controls.enabled = false
  const duration = 2500 // 稍微延长动画时间
  const startTime = Date.now()
  const startPosition = camera.position.clone()
  const startTarget = controls.target.clone()
  const startQuaternion = camera.quaternion.clone()

  const targetQuaternion = new THREE.Quaternion().setFromEuler(new THREE.Euler(targetCameraSettings.rotation.x, targetCameraSettings.rotation.y, targetCameraSettings.rotation.z, 'YXZ'))
  const direction = new THREE.Vector3(0, 0, 1).applyQuaternion(targetQuaternion)
  const targetPosition = targetCameraSettings.target.clone().add(direction.multiplyScalar(targetCameraSettings.distance))

  // 高级缓动函数 - easeInOutCubic
  const easeInOutCubic = (t: number): number => {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2
  }

  // 添加弹性效果的缓动函数
  const easeOutElastic = (t: number): number => {
    const c4 = (2 * Math.PI) / 3
    return t === 0 ? 0 : t === 1 ? 1 : Math.pow(2, -10 * t) * Math.sin((t * 10 - 0.75) * c4) + 1
  }

  const animateCamera = () => {
    const elapsedTime = Date.now() - startTime
    const progress = Math.min(elapsedTime / duration, 1)

    // 使用不同的缓动函数为不同属性
    const positionProgress = easeInOutCubic(progress)
    const rotationProgress = easeInOutCubic(progress)
    const targetProgress = easeInOutCubic(progress)

    controls.target.lerpVectors(startTarget, targetCameraSettings.target, targetProgress)
    camera.position.lerpVectors(startPosition, targetPosition, positionProgress)
    camera.quaternion.slerpQuaternions(startQuaternion, targetQuaternion, rotationProgress)
    controls.update()

    if (progress < 1) {
      requestAnimationFrame(animateCamera)
    } else {
      controls.enabled = true
      console.log('相机动画完成')
      updateCameraInfo()

      // 添加完成动画的微妙反馈
      if (camera) {
        const originalPosition = camera.position.clone()
        const bounceAnimation = () => {
          const bounceTime = Date.now() * 0.01
          const bounceOffset = Math.sin(bounceTime) * 0.001
          camera.position.y = originalPosition.y + bounceOffset

          if (bounceTime < 20) {
            requestAnimationFrame(bounceAnimation)
          } else {
            camera.position.copy(originalPosition)
          }
        }
        bounceAnimation()
      }
    }
  }
  animateCamera()
}

const onWindowResize = () => {
  if (!threeContainer.value || !camera || !renderer) return
  camera.aspect = threeContainer.value.clientWidth / threeContainer.value.clientHeight
  camera.updateProjectionMatrix()
  renderer.setSize(threeContainer.value.clientWidth, threeContainer.value.clientHeight)
  if (popupVisible.value) {
    setTimeout(() => drawSectionView(), 100)
  }
}

// 添加控制球
const addControlsGizmo = () => {
  if (!scene || !renderer || !camera) return
  const gizmoScene = new THREE.Scene()
  const gizmoCamera = new THREE.PerspectiveCamera(50, 1, 0.1, 10)
  gizmoCamera.position.set(0, 0, 2)
  
  const gizmo = new THREE.Group()
  const sphere = new THREE.Mesh(new THREE.SphereGeometry(0.5, 32, 32), new THREE.MeshBasicMaterial({ color: 0xffffff, transparent: true, opacity: 0.15 }))
  gizmo.add(sphere)
  
  const createAxis = (color, rotation, position) => {
    const axis = new THREE.Mesh(new THREE.CylinderGeometry(0.02, 0.02, 0.6, 8), new THREE.MeshBasicMaterial({ color }))
    Object.assign(axis.rotation, rotation)
    Object.assign(axis.position, position)
    gizmo.add(axis)
  }
  createAxis(0xff0000, { z: Math.PI / 2 }, { x: 0.3 })
  createAxis(0x00ff00, {}, { y: 0.3 })
  createAxis(0x0000ff, { x: Math.PI / 2 }, { z: 0.3 })
  
  gizmoScene.add(gizmo)
  
  const gizmoElement = document.createElement('div')
  gizmoElement.style.cssText = 'position: absolute; left: 20px; bottom: 20px; width: 100px; height: 100px; pointer-events: none;'
  threeContainer.value.appendChild(gizmoElement)
  
  const gizmoRenderer = new THREE.WebGLRenderer({ alpha: true, antialias: true })
  gizmoRenderer.setSize(100, 100)
  gizmoRenderer.setClearColor(0x000000, 0)
  gizmoElement.appendChild(gizmoRenderer.domElement)
  
  animationFunctions.push(() => {
    if (camera) {
      gizmo.quaternion.copy(camera.quaternion)
      gizmoRenderer.render(gizmoScene, gizmoCamera)
    }
  })
}

// 更新标记点朝向
const updateMarkersOrientation = () => {
  if (!camera) return
  markers.value.forEach(marker => {
    if (marker.arrowGroup) {
      marker.arrowGroup.children.forEach(child => {
        if (child.geometry && child.geometry.type === 'PlaneGeometry') {
          child.lookAt(camera.position)
        }
      })
    }
  })
}

const cameraInfo = ref({
  rotation: { x: 0, y: 0, z: 0 },
  zoom: 1
})

// 更新相机信息
const updateCameraInfo = () => {
  if (!camera || !controls) return
  const target = controls.target
  
  // 使用不同的方法获取欧拉角，尝试直接从相机矩阵获取
  const matrix = camera.matrixWorld.clone()
  const euler = new THREE.Euler().setFromRotationMatrix(matrix)
  
  // 使用标准方式格式化角度
  const toDegrees = (rad) => {
    return (rad * 180 / Math.PI).toFixed(1)
  }
  
  const distance = camera.position.distanceTo(target)
  cameraInfo.value = {
    rotation: { 
      x: toDegrees(euler.x), 
      y: toDegrees(euler.y), 
      z: toDegrees(euler.z)
    },
    zoom: distance.toFixed(2)
  }
}

// 动画循环
const animate = () => {
  animationFrameId = requestAnimationFrame(animate)
  if (controls) controls.update()
  updateMarkersOrientation()
  updateCameraInfo()
  animationFunctions.forEach(fn => fn())
  if (renderer && scene && camera) renderer.render(scene, camera)
}

onMounted(() => {
  init3DScene()
  fixedMarkersData.forEach(marker => {
    addMarker(new THREE.Vector3(marker.position.x, marker.position.y, marker.position.z), marker.name)
  })
})

onUnmounted(() => {
  if (animationFrameId) cancelAnimationFrame(animationFrameId)
  if (renderer) {
    renderer.domElement.removeEventListener('click', handleModelClick)
    renderer.domElement.removeEventListener('mousemove', handleMouseMove)
    renderer.domElement.removeEventListener('mouseleave', () => { isCoordVisible.value = false })
    
    renderer.dispose()
    if (threeContainer.value) {
      threeContainer.value.removeChild(renderer.domElement)
      const gizmoElement = threeContainer.value.querySelector('div[style*="position: absolute"]')
      if (gizmoElement) threeContainer.value.removeChild(gizmoElement)
    }
  }
  if (controls) controls.dispose()
  window.removeEventListener('resize', onWindowResize)
  
  // 清理趋势图
  if (trendChartInstance.value) {
    trendChartInstance.value.dispose()
    trendChartInstance.value = null
  }
})
</script>

<template>
  <div class="dam-container" ref="threeContainer">
    <!-- 坐标和相机信息面板 -->
    <div
      v-if="isCoordDisplayEnabled"
      class="coord-tooltip"
    >
      <div class="coord-tooltip-title">
        <div class="title-with-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
            <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
            <line x1="12" y1="22.08" x2="12" y2="12"></line>
          </svg>
          <span>三维坐标</span>
        </div>
        <button 
          class="camera-toggle-btn" 
          @click.stop="toggleCameraInfo"
          :title="isCameraInfoVisible ? '隐藏相机信息' : '显示相机信息'"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path v-if="isCameraInfoVisible" d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
            <circle v-if="isCameraInfoVisible" cx="12" cy="12" r="3"></circle>
            <path v-else d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
            <line v-else x1="1" y1="1" x2="23" y2="23"></line>
          </svg>
        </button>
      </div>
      <div class="coord-tooltip-row">
        <span class="coord-label">X:</span>
        <span class="coord-value">{{ mouseCoords3D.x }}</span>
      </div>
      <div class="coord-tooltip-row">
        <span class="coord-label">Y:</span>
        <span class="coord-value">{{ mouseCoords3D.y }}</span>
      </div>
      <div class="coord-tooltip-row">
        <span class="coord-label">Z:</span>
        <span class="coord-value">{{ mouseCoords3D.z }}</span>
      </div>
      
      <!-- 相机信息部分 -->
      <div v-if="isCameraInfoVisible" class="camera-info-section">
        <div class="coord-tooltip-title">
          <div class="title-with-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
              <circle cx="12" cy="13" r="4"></circle>
            </svg>
            <span>相机信息</span>
          </div>
        </div>
        <div class="camera-section-content">
          <div class="camera-section-group">
            <div class="camera-section-title-small">旋转角度</div>
            <div class="coord-tooltip-row">
              <span class="coord-label">X:</span>
              <span class="coord-value">{{ cameraInfo.rotation.x }}°</span>
            </div>
            <div class="coord-tooltip-row">
              <span class="coord-label">Y:</span>
              <span class="coord-value">{{ cameraInfo.rotation.y }}°</span>
            </div>
            <div class="coord-tooltip-row">
              <span class="coord-label">Z:</span>
              <span class="coord-value">{{ cameraInfo.rotation.z }}°</span>
            </div>
          </div>
          
          <div class="camera-section-group">
            <div class="camera-section-title-small">缩放</div>
            <div class="coord-tooltip-row">
              <span class="coord-label">距离:</span>
              <span class="coord-value">{{ cameraInfo.zoom }}</span>
            </div>
          </div>
          
          <div class="camera-info-actions">
            <button class="camera-reset-btn" @click="animateCameraToTarget">
              重置相机
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 坐标显示开关按钮 -->
    <div class="coord-toggle-container">
      <div class="toggle-switch">
        <input 
          type="checkbox" 
          id="coord-toggle" 
          v-model="isCoordDisplayEnabled"
          class="toggle-input"
        />
        <label for="coord-toggle" class="toggle-label">
          <span class="toggle-slider"></span>
          <span class="toggle-text">坐标拾取</span>
        </label>
      </div>
    </div>
    
    <!-- 标记点弹窗 -->
    <div class="marker-popup-overlay" v-if="popupVisible && popupMarker" @click="closePopup">
      <!-- ... existing code ... -->
      <div class="marker-popup" @click.stop>
        <div class="popup-header">
          <span class="popup-title">{{ popupMarker.name || '标记点信息' }}</span>
          <button class="popup-close-btn" @click="closePopup">×</button>
        </div>
        <div class="popup-content">
          <div class="popup-selects">
            <div class="select-group">
              <div class="select-label">具体项目</div>
              <div class="select-wrapper">
                <select class="popup-select">
                  <option value="主坝沉降">主坝沉降</option>
                  <option value="主坝位移">主坝位移</option>
                  <option value="主坝渗流">主坝渗流</option>
                </select>
              </div>
            </div>
            <div class="select-group">
              <div class="select-label">断面</div>
              <div class="select-wrapper">
                <select class="popup-select" :value="popupMarker && popupMarker.name" @change="onSectionChange">
                  <option v-for="marker in fixedMarkersData" :key="marker.name" :value="marker.name">
                    {{ marker.name }}
                  </option>
                </select>
              </div>
            </div>
            <div class="update-time">
              <span class="update-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8"></path><path d="M21 3v5h-5"></path></svg>
              </span>
              <span class="update-text">最后更新时间：</span>
              <span class="update-date">2025-06-15</span>
            </div>
          </div>
          <div class="popup-main">
            <div class="section-view-container">
              <canvas ref="sectionCanvas" class="section-canvas"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 测点趋势图弹窗 -->
    <div class="trend-popup-overlay" v-if="trendPopupVisible && selectedPoint" @click="closeTrendPopup">
      <!-- ... existing code ... -->
      <div class="trend-popup" @click.stop>
        <div class="popup-header">
          <span class="popup-title">{{ selectedPoint.label }}</span>
          <button class="popup-close-btn" @click="closeTrendPopup">×</button>
        </div>
        <div class="trend-popup-content">
          <div class="trend-popup-selects">
            <div class="select-group">
              <div class="select-label">具体项目</div>
              <div class="select-wrapper">
                <select class="popup-select">
                  <option value="主坝沉降">主坝沉降</option>
                  <option value="主坝位移">主坝位移</option>
                  <option value="主坝渗流">主坝渗流</option>
                </select>
              </div>
            </div>
            <div class="select-group">
              <div class="select-label">测点</div>
              <div class="select-wrapper">
                <select class="popup-select" :value="selectedPoint.label">
                  <option v-for="(data, label) in pointHistoryData" :key="label" :value="label">{{ label }}</option>
                </select>
              </div>
            </div>
            <div class="select-group">
              <div class="select-label">时间</div>
              <div class="select-wrapper">
                <select class="popup-select">
                  <option value="自埋设以来">自埋设以来</option>
                  <option value="近一年">近一年</option>
                  <option value="近三年">近三年</option>
                </select>
              </div>
            </div>
          </div>
          <div class="trend-info">
            <div class="trend-info-item">
              <span class="trend-info-label">历史最大测值：</span>
              <span class="trend-info-value">{{ selectedPoint.data.maxValue }}</span>
            </div>
            <div class="trend-info-item">
              <span class="trend-info-label">录于：</span>
              <span class="trend-info-value">{{ selectedPoint.data.recordDate }}</span>
            </div>
          </div>
          <div class="trend-chart-container">
            <div ref="trendChartRef" class="trend-chart"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.dam-container {
  width: 100%; /* Take full width of its parent */
  height: 964px;
  background-color: #f7f7f7;
  position: relative;
  border-radius: 4px 0 0 4px;
  border: 1px solid #e6e6e6;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 坐标和相机信息面板样式 */
.coord-tooltip {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.95);
  color: #333;
  padding: 12px 16px;
  border-radius: 12px;
  font-family: "Microsoft YaHei", sans-serif;
  font-size: 13px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(71, 151, 200, 0.3);
  min-width: 180px;
  max-width: 280px;
  animation: slideInFromRight 0.5s ease-out;
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.coord-tooltip-title {
  font-weight: bold;
  margin-bottom: 8px;
  border-bottom: 1px solid rgba(71, 151, 200, 0.3);
  padding-bottom: 8px;
  color: #4797C8;
  text-align: center;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.coord-tooltip-title span {
  flex-grow: 1;
}

.camera-toggle-btn {
  background: none;
  border: none;
  color: #4797C8;
  font-size: 16px;
  cursor: pointer;
  padding: 2px;
  border-radius: 50%;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.camera-toggle-btn:hover {
  background-color: rgba(71, 151, 200, 0.1);
  transform: scale(1.1);
}

.camera-toggle-btn svg {
  width: 16px;
  height: 16px;
}

.coord-tooltip-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  align-items: center;
}

.coord-label {
  font-weight: 600;
  margin-right: 8px;
  min-width: 40px;
  color: #666;
}

.coord-value {
  font-family: 'Consolas', monospace;
  color: #4797C8;
  font-weight: 600;
  background-color: rgba(71, 151, 200, 0.05);
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid rgba(71, 151, 200, 0.1);
}

/* 相机信息部分样式 */
.camera-info-section {
  margin-top: 12px;
  border-top: 1px solid rgba(71, 151, 200, 0.2);
  padding-top: 12px;
  width: 100%;
}

.camera-section-title {
  font-size: 13px;
  color: #4797C8;
  margin-bottom: 10px;
  text-align: center;
}

.camera-section-content {
  display: flex;
  flex-direction: column;
  gap: 14px;
}

.camera-section-group {
  margin-bottom: 8px;
  background-color: rgba(71, 151, 200, 0.03);
  padding: 8px;
  border-radius: 6px;
  border: 1px solid rgba(71, 151, 200, 0.08);
}

.camera-section-title-small {
  font-size: 12px;
  color: #4797C8;
  margin-bottom: 6px;
  padding-bottom: 4px;
  border-bottom: 1px solid rgba(71, 151, 200, 0.15);
  font-weight: 600;
}

.camera-info-actions {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.camera-reset-btn {
  background-color: #4797C8;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 14px;
  font-family: "Microsoft YaHei";
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 6px rgba(71, 151, 200, 0.3);
}

.camera-reset-btn:hover {
  background-color: #3a89b9;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(71, 151, 200, 0.4);
}

/* 坐标显示开关按钮样式 */
.coord-toggle-container {
  position: absolute;
  bottom: 20px;
  left: 20px;
  z-index: 1000;
  animation: slideInFromLeft 0.5s ease-out;
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.toggle-switch {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.95);
  padding: 10px 14px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(71, 151, 200, 0.3);
  backdrop-filter: blur(16px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.toggle-switch:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
}

.toggle-input {
  display: none;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  user-select: none;
}

.toggle-slider {
  position: relative;
  width: 44px;
  height: 22px;
  background-color: #e0e0e0;
  border-radius: 12px;
  transition: all 0.3s;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggle-slider::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 18px;
  height: 18px;
  background-color: white;
  border-radius: 50%;
  transition: all 0.3s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.toggle-input:checked + .toggle-label .toggle-slider {
  background-color: #4797C8;
}

.toggle-input:checked + .toggle-label .toggle-slider::before {
  transform: translateX(22px);
}

.toggle-text {
  font-family: "Microsoft YaHei";
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* Popup Styles */
.marker-popup-overlay, .trend-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000; /* 确保弹窗在展示面板上方 */
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  animation: overlay-fade 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(4px);
}
@keyframes overlay-fade {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(4px);
  }
}

.marker-popup, .trend-popup {
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  animation: popup-fade 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
@keyframes popup-fade {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.marker-popup { width: 1200px; height: 480px; }
.trend-popup { width: 1200px; height: 700px; }

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #7A96B8;
  color: white;
}
.popup-title {
  font-family: "Microsoft YaHei";
  font-size: 18px;
  font-weight: 600;
}
.popup-close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
}

.popup-content, .trend-popup-content {
  display: flex;
  flex-direction: column;
  height: calc(100% - 54px);
}

.popup-selects, .trend-popup-selects {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #F0F0F0;
}
.select-group {
  display: flex;
  align-items: center;
  margin-right: 30px;
}
.select-label {
  font-family: "Microsoft YaHei";
  font-size: 14px;
  color: #333;
  margin-right: 10px;
  white-space: nowrap;
}
.select-wrapper {
  position: relative;
  width: 300px;
}
.popup-select {
  width: 100%;
  height: 36px;
  border: 1px solid #E1E4E9;
  border-radius: 4px;
  padding: 0 15px;
  font-family: "Microsoft YaHei";
  font-size: 14px;
  background-color: #fff;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23BFBFBF' stroke-width='2'%3e%3cpolyline points='6 9 12 15 18 9'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 10px center;
  cursor: pointer;
}

.update-time {
  display: flex;
  align-items: center;
  margin-left: auto;
  color: #999;
  font-size: 14px;
}
.update-icon { margin-right: 5px; color: #4797C8; }
.update-date { color: #333; }

.popup-main {
  flex: 1;
  padding: 20px;
  overflow: hidden;
}
.section-view-container {
  flex: 1;
  background-color: #F9F9F9;
  border-radius: 4px;
  padding: 10px;
  height: 100%;
}
.section-canvas {
  width: 100%;
  height: 100%;
}

/* Trend Popup Specifics */
.trend-info {
  display: flex;
  padding: 10px 20px;
  background-color: #F9F9F9;
}
.trend-info-item { margin-right: 30px; }
.trend-info-label { font-size: 14px; color: #666; }
.trend-info-value { font-size: 16px; font-weight: 600; color: #333; }
.trend-chart-container {
  flex: 1;
  padding: 10px 20px 20px;
  overflow: hidden;
}
.trend-chart {
  width: 100%;
  height: 100%;
}

.title-with-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.title-with-icon svg {
  color: #4797C8;
}
</style>